import android.content.Context
import com.example.myapplication.core.security.AuthenticationManagerFactory
import com.example.myapplication.core.security.IAuthenticationManagerService
import com.example.myapplication.core.security.TokenManager

import com.example.myapplication.data.remote.api.RetrofitClient
import com.example.myapplication.data.repository.AuthRepositorylmpl
import com.example.myapplication.features.auth.data.remote.api.AuthApiService
import com.example.myapplication.features.auth.domain.repository.AuthRepository
import com.example.myapplication.features.works.data.remote.api.WorkApiService
import com.example.myapplication.features.works.domain.repository.WorksRepositopyImpl
import com.example.myapplication.features.works.domain.repository.WorksRepository


/**
 * container para injeção de dependência
 * */

class  AppContainerDI(private val context: Context)  {
    val tokenManager: TokenManager by lazy {
        TokenManager.getInstance(context)
    }

    init {
        RetrofitClient.init(tokenManager)
    }


    val auhService: AuthApiService by lazy {
        RetrofitClient.create(AuthApiService::class.java)
    }

    val worksService: WorkApiService by lazy {
        RetrofitClient.create(WorkApiService::class.java)
    }


    val authRepository: AuthRepository by lazy {
        AuthRepositorylmpl(auhService)
    }

    val worksRepository: WorksRepository by lazy {
        WorksRepositopyImpl(worksService)
    }

    val authenticationManager: IAuthenticationManagerService = AuthenticationManagerFactory.create(
        tokenManager = tokenManager
    )
}

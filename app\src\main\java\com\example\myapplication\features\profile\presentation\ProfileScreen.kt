package com.example.myapplication.features.profile.presentation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.myapplication.core.utils.formatDate


@Composable
fun ProfileScreen(
    viewModel: ProfileViewModel
) {
    val uiState by viewModel.uiState.collectAsState()


    LaunchedEffect(Unit) {
        viewModel.onEvent(ProfileUiEvent.LoadProfile)
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when {
            uiState.isLoading -> {
                CircularProgressIndicator()
            }
            uiState.error != null -> {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(text = "Erro: ${uiState.error}")
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(onClick = { viewModel.onEvent(ProfileUiEvent.LoadProfile) }) {
                        Text("Tentar novamente")
                    }
                }
            }
            uiState.userProfile != null -> {
                val profile = uiState.userProfile!!
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(text = "Nome: ${profile.fullName}")
                    Text(text = "Usuário: ${profile.username}")
                    Text(text = "Email: ${profile.email}")
                    Text(text = "Ativo: ${if (profile.isActive) "Sim" else "Não"}")
                    Text(text = "Função: ${profile.role}")
                    Text(text = "Criado em: ${formatDate(profile.createdAt)}")
                    Text(text = "Atualizado em: ${formatDate(profile.updatedAt)}")
                }
            }
            else -> {
                Text("Nenhum dado de perfil disponível.")
            }
        }
    }
}

package com.example.myapplication.features.auth.domain.useCase

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.core.constants.AppConstants
import com.example.myapplication.features.auth.domain.model.RegisterRequest
import com.example.myapplication.features.auth.domain.model.RegisterResponse
import com.example.myapplication.features.auth.domain.repository.AuthRepository

class RegisterUseCase(
    private val authRepository: AuthRepository
) {

    suspend operator fun invoke(
        username: String,
        email: String,
        password: String,
        fullName: String? = null,
    ): ResultAsyncState<RegisterResponse> {
        when {
            username.isBlank() -> return ResultAsyncState.Error("Nome de usuário é obrigatório")
            username.length < AppConstants.Validation.MIN_USERNAME_LENGTH ->
                return ResultAsyncState.Error("Nome de usuário deve ter pelo menos ${AppConstants.Validation.MIN_USERNAME_LENGTH} caracteres")
            username.length > 30 -> return ResultAsyncState.Error("Nome de usuário deve ter no máximo 30 caracteres")
            !username.matches(Regex("^[a-zA-Z0-9_]+$")) ->
                return ResultAsyncState.Error("Nome de usuário deve conter apenas letras, números e underscore")

            email.isBlank() -> return ResultAsyncState.Error("Email é obrigatório")
            !isValidEmail(email) -> return ResultAsyncState.Error("Email deve ser um email válido")

            password.isBlank() -> return ResultAsyncState.Error("Senha é obrigatória")
            password.length < AppConstants.Validation.MIN_PASSWORD_LENGTH ->
                return ResultAsyncState.Error("Senha deve ter pelo menos ${AppConstants.Validation.MIN_PASSWORD_LENGTH} caracteres")

            password.length > 100 -> return ResultAsyncState.Error("Senha deve ter no máximo 100 caracteres")
            fullName != null && fullName.isNotBlank() && fullName.length > 100 ->
                return ResultAsyncState.Error("Nome completo deve ter no máximo 100 caracteres")
        }

        val registerRequest = RegisterRequest(
            username = username.trim(),
            email = email.trim().lowercase(),
            password = password,
            fullName = fullName?.trim()?.takeIf { it.isNotBlank() }
        )

        return authRepository.register(registerRequest)
    }



    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}

package com.example.myapplication.features.auth.domain.useCase

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.auth.data.remote.dto.profile.ProfileResponseDto


import com.example.myapplication.features.auth.domain.repository.AuthRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow


class GetProfileUseCase(private val authRepository: AuthRepository)
{
    operator fun invoke(): Flow<ResultAsyncState<ProfileResponseDto>> = flow {
        try {
            val response = authRepository.getUserProfile()
            emit(response)
        } catch (e: Exception) {
            emit(ResultAsyncState.Error("Erro ao obter perfil: ${e.message}"))
        }
    }
}
&com.example.myapplication.MainActivity>com.example.myapplication.core.common.ResultAsyncState.Success<com.example.myapplication.core.common.ResultAsyncState.Error>com.example.myapplication.core.common.ResultAsyncState.LoadingScreen.SplashScreenScreen.LoginScreenScreen.HomeScreenScreen.RegisterScreen6com.example.myapplication.core.network.AuthInterceptor=com.example.myapplication.core.security.AuthenticationManager4com.example.myapplication.core.security.TokenManager<com.example.myapplication.data.repository.AuthRepositorylmpl^com.example.myapplication.features.auth.presentation.login.LoginUiEvent.EmailOrUsernameChangedWcom.example.myapplication.features.auth.presentation.login.LoginUiEvent.PasswordChangedNcom.example.myapplication.features.auth.presentation.login.LoginUiEvent.SubmitRcom.example.myapplication.features.auth.presentation.login.LoginUiEvent.ClearErrorNcom.example.myapplication.features.auth.presentation.login.LoginResult.SuccessLcom.example.myapplication.features.auth.presentation.login.LoginResult.ErrorIcom.example.myapplication.features.auth.presentation.login.LoginViewModelPcom.example.myapplication.features.auth.presentation.login.LoginViewModelFactory]com.example.myapplication.features.auth.presentation.register.RegisterUiEvent.UsernameChangedZcom.example.myapplication.features.auth.presentation.register.RegisterUiEvent.EmailChanged]com.example.myapplication.features.auth.presentation.register.RegisterUiEvent.PasswordChanged]com.example.myapplication.features.auth.presentation.register.RegisterUiEvent.FullNameChangeddcom.example.myapplication.features.auth.presentation.register.RegisterUiEvent.ConfirmPasswordChangedTcom.example.myapplication.features.auth.presentation.register.RegisterUiEvent.SubmitXcom.example.myapplication.features.auth.presentation.register.RegisterUiEvent.ClearErrorTcom.example.myapplication.features.auth.presentation.register.RegisterResult.SuccessRcom.example.myapplication.features.auth.presentation.register.RegisterResult.ErrorOcom.example.myapplication.features.auth.presentation.register.RegisterViewModelVcom.example.myapplication.features.auth.presentation.register.RegisterViewModelFactoryJcom.example.myapplication.features.home.presentation.HomeNavItem.DashboardHcom.example.myapplication.features.home.presentation.HomeNavItem.ProfileIcom.example.myapplication.features.home.presentation.HomeNavItem.SettingsRcom.example.myapplication.features.profile.presentation.ProfileUiEvent.LoadProfileQcom.example.myapplication.features.profile.presentation.ProfileUiEvent.ClearErrorHcom.example.myapplication.features.profile.presentation.ProfileViewModelOcom.example.myapplication.features.profile.presentation.ProfileViewModelFactory><EMAIL>@com.example.myapplication.features.works.domain.enums.WorkSortByCcom.example.myapplication.features.works.domain.enums.WorkSortOrderGcom.example.myapplication.features.works.domain.model.WorkPaginatedData>com.example.myapplication.features.works.domain.model.GetWorksNcom.example.myapplication.features.works.domain.repository.WorksRepositopyImplOcom.example.myapplication.features.works.presentation.workList.AddWorkViewModelXcom.example.myapplication.features.works.presentation.workList.WorkListUiEvent.LoadWorksYcom.example.myapplication.features.works.presentation.workList.WorkListUiEvent.ClearErrorPcom.example.myapplication.features.works.presentation.workList.WorkListViewModelWcom.example.myapplication.features.works.presentation.workList.WorkListViewModelFactoryQcom.example.myapplication.features.home.presentation.HomeNavigationState.WorkListScom.example.myapplication.features.home.presentation.HomeNavigationState.WorkDetailQcom.example.myapplication.features.home.presentation.HomeNavigationState.EditWorkRcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent[com.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.LoadWork]com.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.UpdateWork]com.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.DeleteWork`com.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.CreateChapter`com.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.SetCoverImageccom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.RemoveCoverImageccom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.AddGalleryImagesecom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.RemoveGalleryImage]com.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.ClearError_com.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent.ClearSuccessTcom.example.myapplication.features.works.presentation.workDetail.WorkDetailViewModel[com.example.myapplication.features.works.presentation.workDetail.WorkDetailViewModelFactory                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
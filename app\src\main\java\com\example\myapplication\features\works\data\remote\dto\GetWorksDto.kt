package com.example.myapplication.features.works.data.remote.dto
import com.google.gson.annotations.SerializedName

data class WorkDto(
    @SerializedName("id") val id: String,
    @SerializedName("title") val title: String,
    @SerializedName("originalTitle") val originalTitle: String,
    @SerializedName("description") val description: String,
    @SerializedName("coverImage") val coverImage: String,
    @SerializedName("type") val type: String,
    @SerializedName("status") val status: String,
    @SerializedName("author") val author: String,
    @SerializedName("artist") val artist: String,
    @SerializedName("totalChapters") val totalChapters: Int,
    @SerializedName("releaseDate") val releaseDate: String,
    @SerializedName("averageRating") val averageRating: Double,
    @SerializedName("totalReviews") val totalReviews: Int,
    @SerializedName("createdAt") val createdAt: String,
    @SerializedName("updatedAt") val updatedAt: String
)

data class GetWorksDto(
    @SerializedName("total") val total: Int,
    @SerializedName("page") val page: Int,
    @SerializedName("limit") val limit: Int,
    @SerializedName("totalPages") val totalPages: Int,
    @SerializedName("works") val works: List<WorkDto>
)
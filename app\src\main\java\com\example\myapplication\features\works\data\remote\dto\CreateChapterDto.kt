package com.example.myapplication.features.works.data.remote.dto

import com.google.gson.annotations.SerializedName

data class CreateChapterDto(
    @SerializedName("workId")
    val workId: String,
    @SerializedName("title")
    val title: String,
    @SerializedName("releaseDate")
    val releaseDate: String,
    @SerializedName("url")
    val url: String
)

data class ChapterDto(
    @SerializedName("id")
    val id: String,
    @SerializedName("workId")
    val workId: String,
    @SerializedName("title")
    val title: String,
    @SerializedName("releaseDate")
    val releaseDate: String,
    @SerializedName("url")
    val url: String,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
)

package com.example.myapplication.features.auth.presentation.register

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.core.security.IAuthenticationManagerService
import com.example.myapplication.features.auth.domain.useCase.RegisterUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class RegisterUiState(
    val username: String = "",
    val email: String = "",
    val password: String = "",
    val confirmPassword: String = "",
    val fullName: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null
)

sealed class RegisterUiEvent {
    data class UsernameChanged(val value: String) : RegisterUiEvent()
    data class EmailChanged(val value: String) : RegisterUiEvent()
    data class PasswordChanged(val value: String) : RegisterUiEvent()
    data class FullNameChanged(val value: String) : RegisterUiEvent()
    data class ConfirmPasswordChanged(val value: String) : RegisterUiEvent()
    object Submit : RegisterUiEvent()
    object ClearError : RegisterUiEvent()
}

sealed class RegisterResult {
    data class Success(val message: String) : RegisterResult()
    data class Error(val message: String) : RegisterResult()
}


class RegisterViewModel(
    private val registerUseCase: RegisterUseCase,
    private val authenticationManager: IAuthenticationManagerService
): ViewModel() {
    private val _uiState = MutableStateFlow(RegisterUiState())
    val uiState: StateFlow<RegisterUiState> = _uiState.asStateFlow()

    fun onEvent(event: RegisterUiEvent) {
        println(
            "RegisterViewModel.onEvent: Received event: $event"
        )
        when (event) {
            is RegisterUiEvent.UsernameChanged -> {
                _uiState.value = _uiState.value.copy(
                    username = event.value,
                    errorMessage = null
                )
            }
            is RegisterUiEvent.EmailChanged -> {
                _uiState.value = _uiState.value.copy(
                    email = event.value,
                    errorMessage = null
                )
            }
            is RegisterUiEvent.PasswordChanged -> {
                _uiState.value = _uiState.value.copy(
                    password = event.value,
                    errorMessage = null
                )
            }
            is RegisterUiEvent.FullNameChanged -> {
                _uiState.value = _uiState.value.copy(
                    fullName = event.value,
                    errorMessage = null
                )
            }
            is RegisterUiEvent.ConfirmPasswordChanged -> {
                _uiState.value = _uiState.value.copy(
                    confirmPassword = event.value,
                    errorMessage = null
                )
            }
            is RegisterUiEvent.Submit -> {
                submitRegistration()
            }
            is RegisterUiEvent.ClearError -> {
                _uiState.value = _uiState.value.copy(
                    errorMessage = null
                )
            }
        }
    }

    private fun submitRegistration() {
        var currentState = _uiState.value

        if (currentState.isLoading) return

        if (currentState.username.isBlank() || currentState.email.isBlank() ||
            currentState.password.isBlank() || currentState.confirmPassword.isBlank() ||
            currentState.fullName.isBlank()
        ) {
            _uiState.value = currentState.copy(
                errorMessage = "Todos os campos são obrigatórios"
            )
            return
        }

        if (currentState.password != currentState.confirmPassword) {
            _uiState.value = currentState.copy(
                errorMessage = "As senhas não coincidem"
            )
            return
        }

        _uiState.value = currentState.copy(isLoading = true, errorMessage = null)


        viewModelScope.launch {
            val result = registerUseCase(
                username = _uiState.value.username,
                email = _uiState.value.email,
                password = _uiState.value.password,
                fullName = _uiState.value.fullName
            )

            when (result) {
                is ResultAsyncState.Success -> {
                    val saveResult = authenticationManager.handleRegisterSuccess(result.data!!)
                    when (saveResult) {
                        is ResultAsyncState.Success -> {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                successMessage = "Registro realizado com sucesso!"
                            )
                        }
                        is ResultAsyncState.Error -> {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                errorMessage = saveResult.message
                            )
                        }
                        is ResultAsyncState.Loading<*> -> {
                            _uiState.value = _uiState.value.copy(
                                isLoading = true,
                                errorMessage = null,
                                successMessage = null
                            )
                        }
                    }
                }
                is ResultAsyncState.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = result.message
                    )
                }
                is ResultAsyncState.Loading<*> -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = true,
                        errorMessage = null,
                        successMessage = null
                    )
                }
            }
        }
    }
}

package com.example.myapplication.features.auth.presentation.login

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.core.security.IAuthenticationManagerService
import com.example.myapplication.features.auth.domain.useCase.LoginUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class LoginUiState(
    val usernameOrEmail: String = "",
    val password: String = "",
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    val loginResult: LoginResult? = null
)

sealed class LoginUiEvent {
    data class EmailOrUsernameChanged(val value: String) : LoginUiEvent()
    data class PasswordChanged(val value: String) : LoginUiEvent()
    data object Submit : LoginUiEvent()
    data object ClearError : LoginUiEvent()
}

sealed class LoginResult {
    data class Success(val message: String) : LoginResult()
    data class Error(val message: String) : LoginResult()
}


class LoginViewModel(
    private val loginUseCase: LoginUseCase,
    private val authenticationManager: IAuthenticationManagerService
) : ViewModel() {
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()

    fun onEvent(event: LoginUiEvent) {
        when (event) {
            is LoginUiEvent.EmailOrUsernameChanged -> {
                _uiState.value = _uiState.value.copy(
                    usernameOrEmail = event.value,
                    errorMessage = null
                )
            }
            is LoginUiEvent.PasswordChanged -> {
                _uiState.value = _uiState.value.copy(
                    password = event.value,
                    errorMessage = null
                )
            }
            is LoginUiEvent.Submit -> {
                submitLogin()
            }

            is LoginUiEvent.ClearError -> {
                _uiState.value = _uiState.value.copy(
                    errorMessage = null
                )
            }
        }
    }

    private fun submitLogin() {
        val currentState = _uiState.value

        if (currentState.isLoading) return

        if (currentState.usernameOrEmail.isBlank() || currentState.password.isBlank()) {
            _uiState.value = currentState.copy(
                errorMessage = "Preencha todos os campos."
            )
            return
        }

        _uiState.value = currentState.copy(isLoading = true, errorMessage = null, loginResult = null)

        viewModelScope.launch {
            val result = loginUseCase(
                usernameOrEmail = _uiState.value.usernameOrEmail,
                password =  _uiState.value.password
            )

            when (result) {
                is ResultAsyncState.Success -> {
                    val saveResult = authenticationManager.handleLoginSuccess(result.data!!)
                    when (saveResult) {
                        is ResultAsyncState.Success -> {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                loginResult = LoginResult.Success("Login realizado com sucesso!")
                            )
                        }
                        is ResultAsyncState.Error -> {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                errorMessage = saveResult.message ?: "Erro ao salvar os dados do usuário."
                            )
                        }
                        else -> {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                errorMessage = "Erro desconhecido ao salvar os dados do usuário."
                            )
                        }
                    }
                }
                is ResultAsyncState.Error -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = result.message ?: "Erro ao realizar o login."
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "Erro desconhecido ao realizar o login."
                    )
                }
            }
        }
    }
}
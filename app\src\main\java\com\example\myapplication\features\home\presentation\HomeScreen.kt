package com.example.myapplication.features.home.presentation

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
//import androidx.compose.ui.size
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import androidx.compose.material3.Button
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import AppContainerDI
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.myapplication.features.profile.presentation.ProfileScreen
import com.example.myapplication.features.profile.presentation.ProfileViewModel
import com.example.myapplication.features.profile.presentation.ProfileViewModelFactory
import com.example.myapplication.features.works.presentation.workList.WorkListScreen
import com.example.myapplication.features.works.presentation.workList.WorkListViewModel
import com.example.myapplication.features.works.presentation.workList.WorkListViewModelFactory
import com.example.myapplication.features.works.presentation.workDetail.WorkDetailScreen
import com.example.myapplication.features.works.presentation.workDetail.WorkDetailViewModel
import com.example.myapplication.features.works.presentation.workDetail.WorkDetailViewModelFactory
import com.example.myapplication.features.works.data.remote.dto.WorkDto

sealed class HomeNavItem(val label: String, val icon: ImageVector) {
    object Dashboard : HomeNavItem("Dashboard", Icons.Default.Home)
    object Profile : HomeNavItem("Perfil", Icons.Default.Person)
    object Settings : HomeNavItem("Configurações", Icons.Default.Settings)
}

// Estados de navegação interna
sealed class HomeNavigationState {
    object WorkList : HomeNavigationState()
    data class WorkDetail(val workId: String) : HomeNavigationState()
    data class EditWork(val work: WorkDto) : HomeNavigationState()
}

@Composable
fun HomeScreen(appContainer: AppContainerDI, username: String? = null, onLogout: () -> Unit = {}) {
    var selectedItem by remember { mutableStateOf<HomeNavItem>(HomeNavItem.Dashboard) }
    var navigationState by remember { mutableStateOf<HomeNavigationState>(HomeNavigationState.WorkList) }

    Scaffold(
        bottomBar = {
            // Só mostra a bottom bar quando não estiver em tela de detalhes
            if (navigationState is HomeNavigationState.WorkList || selectedItem != HomeNavItem.Dashboard) {
                NavigationBar {
                    val items = listOf(
                        HomeNavItem.Dashboard,
                        HomeNavItem.Profile,
                        HomeNavItem.Settings
                    )
                    items.forEach { item ->
                        NavigationBarItem(
                            icon = { androidx.compose.material3.Icon(item.icon, contentDescription = item.label) },
                            label = { Text(item.label) },
                            selected = selectedItem == item,
                            onClick = {
                                selectedItem = item
                                if (item == HomeNavItem.Dashboard) {
                                    navigationState = HomeNavigationState.WorkList
                                }
                            }
                        )
                    }
                }
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize().padding(innerPadding)) {
            when (selectedItem) {
                is HomeNavItem.Dashboard -> {
                    DashboardContent(
                        appContainer = appContainer,
                        navigationState = navigationState,
                        onNavigateToWorkDetail = { workId ->
                            navigationState = HomeNavigationState.WorkDetail(workId)
                        },
                        onNavigateBack = {
                            navigationState = HomeNavigationState.WorkList
                        },
                        onEditWork = { work ->
                            navigationState = HomeNavigationState.EditWork(work)
                        }
                    )
                }
                is HomeNavItem.Profile -> {
                    val profileViewModel: ProfileViewModel = viewModel(
                        factory = ProfileViewModelFactory(appContainer)
                    )
                    ProfileScreen(profileViewModel)
                }
                is HomeNavItem.Settings -> {
                    Text(
                        text = "Configurações",
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }

            // Botão de logout só aparece quando não estiver em detalhes
            if (navigationState is HomeNavigationState.WorkList || selectedItem != HomeNavItem.Dashboard) {
                Button(
                    onClick = onLogout,
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .offset(y = (-56).dp)
                ) {
                    Text(text = "Sair")
                }
            }
        }
    }
}

@Composable
private fun DashboardContent(
    appContainer: AppContainerDI,
    navigationState: HomeNavigationState,
    onNavigateToWorkDetail: (String) -> Unit,
    onNavigateBack: () -> Unit,
    onEditWork: (WorkDto) -> Unit
) {
    when (navigationState) {
        is HomeNavigationState.WorkList -> {
            val workListViewModel: WorkListViewModel = viewModel(
                factory = WorkListViewModelFactory(appContainer)
            )
            WorkListScreenWithNavigation(
                viewModel = workListViewModel,
                onWorkClick = onNavigateToWorkDetail
            )
        }
        is HomeNavigationState.WorkDetail -> {
            val workDetailViewModel: WorkDetailViewModel = viewModel(
                factory = WorkDetailViewModelFactory(appContainer)
            )
            WorkDetailScreen(
                viewModel = workDetailViewModel,
                workId = navigationState.workId,
                onNavigateBack = onNavigateBack,
                onEditWork = onEditWork
            )
        }
        is HomeNavigationState.EditWork -> {
            // Implementar tela de edição futuramente
            Text(
                text = "Tela de edição em desenvolvimento",
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Composable
private fun WorkListScreenWithNavigation(
    viewModel: WorkListViewModel,
    onWorkClick: (String) -> Unit
) {
    // Reutiliza a lógica do WorkListScreen original mas adiciona navegação
    val uiState by viewModel.uiState.collectAsState()
    val addWorkViewModel: com.example.myapplication.features.works.presentation.workList.AddWorkViewModel = viewModel()

    LaunchedEffect(Unit) {
        viewModel.onEvent(com.example.myapplication.features.works.presentation.workList.WorkListUiEvent.LoadWorks)
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when {
            uiState.isLoading -> {
                androidx.compose.material3.CircularProgressIndicator()
            }
            uiState.error != null -> {
                androidx.compose.foundation.layout.Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(text = "Erro: ${uiState.error}")
                    androidx.compose.foundation.layout.Spacer(modifier = Modifier.height(8.dp))
                    androidx.compose.material3.Button(onClick = { viewModel.onEvent(com.example.myapplication.features.works.presentation.workList.WorkListUiEvent.LoadWorks) }) {
                        Text("Tentar novamente")
                    }
                }
            }
            true -> {
                val works = uiState.works.data?.works
                if (works?.isNotEmpty() == true) {
                    androidx.compose.foundation.layout.Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        works.forEach { work ->
                            androidx.compose.material3.Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(8.dp)
                                    .clickable { onWorkClick(work.id) }, // Adiciona navegação
                                colors = androidx.compose.material3.CardDefaults.cardColors(
                                    containerColor = androidx.compose.ui.graphics.Color(0xFFF5F5F5)
                                ),
                                elevation = androidx.compose.material3.CardDefaults.elevatedCardElevation(8.dp),
                                shape = androidx.compose.foundation.shape.RoundedCornerShape(16.dp)
                            ) {
                                androidx.compose.foundation.layout.Row(modifier = Modifier.padding(16.dp)) {
                                    // Imagem da obra
                                    coil.compose.AsyncImage(
                                        model = work.coverImage,
                                        contentDescription = "Capa de ${work.title}",
                                        modifier = Modifier
                                            .size(80.dp)
                                            .clip(androidx.compose.foundation.shape.RoundedCornerShape(8.dp)),
                                        contentScale = androidx.compose.ui.layout.ContentScale.Crop
                                    )

                                    androidx.compose.foundation.layout.Spacer(modifier = Modifier.width(12.dp))

                                    // Informações da obra
                                    androidx.compose.foundation.layout.Column(
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        Text(
                                            text = work.title,
                                            fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                            fontSize = 16.sp,
                                            maxLines = 2,
                                            overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                                        )

                                        androidx.compose.foundation.layout.Spacer(modifier = Modifier.height(4.dp))

                                        Text(
                                            text = "Autor: ${work.author}",
                                            fontSize = 14.sp,
                                            color = androidx.compose.ui.graphics.Color.Gray
                                        )

                                        Text(
                                            text = "Status: ${work.status}",
                                            fontSize = 14.sp,
                                            color = androidx.compose.ui.graphics.Color.Gray
                                        )

                                        androidx.compose.foundation.layout.Spacer(modifier = Modifier.height(4.dp))

                                        androidx.compose.foundation.layout.Row(
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            androidx.compose.material3.Icon(
                                                imageVector = androidx.compose.material.icons.Icons.Default.Star,
                                                contentDescription = "Avaliação",
                                                tint = androidx.compose.ui.graphics.Color(0xFFFFD700),
                                                modifier = Modifier.size(16.dp)
                                            )
                                            androidx.compose.foundation.layout.Spacer(modifier = Modifier.width(4.dp))
                                            Text(
                                                text = "${work.averageRating}/5.0",
                                                fontSize = 12.sp,
                                                color = androidx.compose.ui.graphics.Color.Gray
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    Text("Nenhuma obra disponível.")
                }
            }
        }
    }

    // Botão flutuante para adicionar obra
    androidx.compose.material3.FloatingActionButton(
        onClick = { addWorkViewModel.openDialog() },
        modifier = Modifier.padding(16.dp)
    ) {
        androidx.compose.material3.Icon(
            imageVector = androidx.compose.material.icons.Icons.Default.Add,
            contentDescription = "Adicionar Obra"
        )
    }

    // Modal para adicionar obra
    com.example.myapplication.features.works.presentation.workList.AddWorkDialog(
        show = addWorkViewModel.showDialog,
        onDismiss = { addWorkViewModel.closeDialog() },
        onAdd = { addWorkViewModel.addWork() }
    )
}



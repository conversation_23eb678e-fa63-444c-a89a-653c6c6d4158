package com.example.myapplication.features.auth.domain.repository

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.auth.data.remote.dto.profile.ProfileResponseDto
import com.example.myapplication.features.auth.domain.model.LoginRequest
import com.example.myapplication.features.auth.domain.model.LoginResponse
import com.example.myapplication.features.auth.domain.model.ProfileResponse
import com.example.myapplication.features.auth.domain.model.RegisterRequest
import com.example.myapplication.features.auth.domain.model.RegisterResponse

interface AuthRepository {
    suspend fun login(loginRequest: LoginRequest): ResultAsyncState<LoginResponse>
    suspend fun register(registerRequest: RegisterRequest): ResultAsyncState<RegisterResponse>
    suspend fun getUserProfile(): ResultAsyncState<ProfileResponseDto>
}
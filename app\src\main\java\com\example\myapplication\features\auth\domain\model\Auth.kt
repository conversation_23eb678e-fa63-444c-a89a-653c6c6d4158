package com.example.myapplication.features.auth.domain.model

data class <PERSON>ginRequest(
    val usernameOrEmail: String,
    val password: String
)


data class LoginResponse(
    val accessToken: String,
    val refreshToken: String,
    val tokenType: String,
    val expiresIn: Long,
    val user: UserLogin
)

data class AuthToken(
    val accessToken: String,
    val refreshToken: String,
    val tokenType: String,
    val expiresIn: Long
)

data class AuthError(
    val statusCode: Int,
    val message: String,
    val error: String,
    val code: String? = null
)
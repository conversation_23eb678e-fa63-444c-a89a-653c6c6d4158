# Works Module - API Routes Implementation

Este módulo implementa todas as rotas da API para gerenciamento de obras (manhwa, manga, manhua).

## Rotas Implementadas

### 1. Obras (Works)
- **POST /works** - Criar nova obra
- **GET /works** - Listar obras com filtros e paginação
- **PUT /works/{id}** - Atualizar obra existente
- **GET /works/{id}** - Buscar obra por ID
- **DELETE /works/{id}** - Deletar obra

### 2. <PERSON><PERSON>tulos (Chapters)
- **POST /works/chapters** - Criar novo capítulo

### 3. Imagens (Images)
- **POST /works/{id}/cover-image** - Associar imagem de capa
- **DELETE /works/{id}/cover-image** - Remover imagem de capa
- **POST /works/{id}/gallery-images** - Associar imagens à galeria
- **DELETE /works/{id}/gallery-images/{imageId}** - Remover imagem da galeria

## Estrutura do Módulo

### Data Layer
- **DTOs**: Objetos de transferência de dados para comunicação com a API
  - `CreateWorkDto`, `UpdateWorkDto`, `WorkDto`
  - `CreateChapterDto`, `ChapterDto`
  - `SetCoverImageDto`, `AddGalleryImagesDto`
  - `GetWorksParametersDto`, `GetWorksDto`

- **API Service**: Interface Retrofit para comunicação com a API
  - `WorkApiService` - Define todos os endpoints

- **Repository Implementation**: Implementação concreta do repositório
  - `WorksRepositoryImpl` - Implementa todas as operações da API

- **Mappers**: Conversão entre DTOs e modelos de domínio
  - `WorkMapper` - Funções de extensão para mapeamento

### Domain Layer
- **Models**: Modelos de domínio
  - `Work`, `CreateWork`, `UpdateWork`
  - `Chapter`, `CreateChapter`
  - `SetCoverImage`, `AddGalleryImages`
  - `WorkParameters`

- **Repository Interface**: Contrato do repositório
  - `WorksRepository` - Define todas as operações

- **Use Cases**: Casos de uso para cada operação
  - `GetWorksUseCase`, `GetWorkByIdUseCase`
  - `UpdateWorkUseCase`, `DeleteWorkUseCase`
  - `CreateChapterUseCase`
  - `SetCoverImageUseCase`, `RemoveCoverImageUseCase`
  - `AddGalleryImagesUseCase`, `RemoveGalleryImageUseCase`

- **Enums**: Enumerações para tipos e status
  - `WorkType`, `WorkStatus`, `WorkSortBy`, `WorkSortOrder`

### Presentation Layer
- **ViewModels**: Gerenciamento de estado da UI
  - `WorkListViewModel` - Lista de obras
  - `WorkDetailViewModel` - Detalhes e operações de uma obra

- **UI States e Events**: Estados e eventos da interface
  - Estados para loading, error, success
  - Eventos para todas as operações CRUD

## Funcionalidades Principais

### Filtros e Busca
- Filtro por tipo (manhwa, manga, manhua)
- Filtro por status (ongoing, completed, hiatus, cancelled)
- Busca por autor e artista
- Busca por título ou descrição
- Filtro por tags
- Filtro por avaliação (min/max)
- Paginação com limite configurável
- Ordenação por diferentes campos

### Operações CRUD
- Criar, ler, atualizar e deletar obras
- Gerenciar capítulos
- Gerenciar imagens de capa e galeria
- Tratamento de erros consistente
- Estados de loading para todas as operações

### Arquitetura
- Segue os princípios SOLID
- Clean Architecture com separação clara de camadas
- Injeção de dependência preparada
- Tratamento de erros padronizado
- Uso de Flow para programação reativa

## Como Usar

### Exemplo de Uso do ViewModel
```kotlin
// Carregar obra por ID
viewModel.onEvent(WorkDetailUiEvent.LoadWork("work-id"))

// Atualizar obra
val updateDto = UpdateWorkDto(...)
viewModel.onEvent(WorkDetailUiEvent.UpdateWork("work-id", updateDto))

// Deletar obra
viewModel.onEvent(WorkDetailUiEvent.DeleteWork("work-id"))

// Criar capítulo
val chapterDto = CreateChapterDto(...)
viewModel.onEvent(WorkDetailUiEvent.CreateChapter(chapterDto))
```

### Exemplo de Filtros
```kotlin
val params = GetWorksParametersDto(
    type = WorkType.MANHWA,
    status = WorkStatus.ONGOING,
    author = "Autor",
    search = "título",
    minRating = 4.0f,
    page = 1,
    limit = 20
)
```

## Próximos Passos
1. Implementar testes unitários para todos os use cases
2. Criar interfaces de usuário para as novas funcionalidades
3. Implementar cache local para melhor performance
4. Adicionar validações de entrada
5. Implementar upload de imagens

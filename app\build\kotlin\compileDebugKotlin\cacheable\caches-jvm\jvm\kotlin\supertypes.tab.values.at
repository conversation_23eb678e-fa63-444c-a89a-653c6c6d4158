/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity7 6com.example.myapplication.core.common.ResultAsyncState7 6com.example.myapplication.core.common.ResultAsyncState7 6com.example.myapplication.core.common.ResultAsyncState Screen Screen Screen Screen okhttp3.InterceptorF Ecom.example.myapplication.core.security.IAuthenticationManagerService= <com.example.myapplication.core.security.ITokenManagerServiceI Hcom.example.myapplication.features.auth.domain.repository.AuthRepositoryH Gcom.example.myapplication.features.auth.presentation.login.LoginUiEventH Gcom.example.myapplication.features.auth.presentation.login.LoginUiEventH Gcom.example.myapplication.features.auth.presentation.login.LoginUiEventH Gcom.example.myapplication.features.auth.presentation.login.LoginUiEventG Fcom.example.myapplication.features.auth.presentation.login.LoginResultG Fcom.example.myapplication.features.auth.presentation.login.LoginResult androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.FactoryN Mcom.example.myapplication.features.auth.presentation.register.RegisterUiEventN Mcom.example.myapplication.features.auth.presentation.register.RegisterUiEventN Mcom.example.myapplication.features.auth.presentation.register.RegisterUiEventN Mcom.example.myapplication.features.auth.presentation.register.RegisterUiEventN Mcom.example.myapplication.features.auth.presentation.register.RegisterUiEventN Mcom.example.myapplication.features.auth.presentation.register.RegisterUiEventN Mcom.example.myapplication.features.auth.presentation.register.RegisterUiEventM Lcom.example.myapplication.features.auth.presentation.register.RegisterResultM Lcom.example.myapplication.features.auth.presentation.register.RegisterResult androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.FactoryA @com.example.myapplication.features.home.presentation.HomeNavItemA @com.example.myapplication.features.home.presentation.HomeNavItemA @com.example.myapplication.features.home.presentation.HomeNavItemG Fcom.example.myapplication.features.profile.presentation.ProfileUiEventG Fcom.example.myapplication.features.profile.presentation.ProfileUiEvent androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum4 3com.example.myapplication.core.common.PaginatedDataH Gcom.example.myapplication.features.works.domain.model.WorkPaginatedDataK Jcom.example.myapplication.features.works.domain.repository.WorksRepository androidx.lifecycle.ViewModelO Ncom.example.myapplication.features.works.presentation.workList.WorkListUiEventO Ncom.example.myapplication.features.works.presentation.workList.WorkListUiEvent androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.FactoryK Jcom.example.myapplication.features.works.domain.repository.WorksRepositoryK Jcom.example.myapplication.features.works.domain.repository.WorksRepositoryK Jcom.example.myapplication.features.works.domain.repository.WorksRepositoryK Jcom.example.myapplication.features.works.domain.repository.WorksRepositoryI Hcom.example.myapplication.features.home.presentation.HomeNavigationStateI Hcom.example.myapplication.features.home.presentation.HomeNavigationStateI Hcom.example.myapplication.features.home.presentation.HomeNavigationState. -com.example.myapplication.core.common.UiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEventS Rcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.FactoryA @com.example.myapplication.features.home.presentation.HomeNavItemA @com.example.myapplication.features.home.presentation.HomeNavItemA @com.example.myapplication.features.home.presentation.HomeNavItemK Jcom.example.myapplication.features.works.domain.repository.WorksRepository- ,androidx.lifecycle.ViewModelProvider.Factory
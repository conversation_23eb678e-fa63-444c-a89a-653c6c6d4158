package com.example.myapplication.features.works.domain.useCase

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.domain.repository.WorksRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class DeleteWorkUseCase(
    private val worksRepository: WorksRepository
) {
    operator fun invoke(id: String): Flow<ResultAsyncState<Unit>> = flow {
        try {
            emit(ResultAsyncState.Loading())
            val response = worksRepository.deleteWork(id)
            emit(response)
        } catch (e: Exception) {
            emit(ResultAsyncState.Error("Erro ao deletar obra: ${e.message}"))
        }
    }
}

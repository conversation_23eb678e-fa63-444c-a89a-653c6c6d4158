package com.example.myapplication.features.auth.data.remote.dto.login

import com.google.gson.annotations.SerializedName

data class LoginResponseDto(
    @SerializedName("access_token")
    val accessToken: String,

    @SerializedName("refresh_token")
    val refreshToken: String,

    @SerializedName("token_type")
    val tokenType: String,

    @SerializedName("expires_in")
    val expiresIn: Long,

    @SerializedName("user")
    val user: UserLoginDto?
)


data class UserLoginDto(
    @SerializedName("id")
    val id: Long,

    @SerializedName("username")
    val username: String,

    @SerializedName("email")
    val email: String,

    @SerializedName("fullName")
    val fullName: String,

    @SerializedName("avatar")
    val avatar: String?,

    @SerializedName("isActive")
    val isActive: Bo<PERSON>an,

    @SerializedName("role")
    val role: String? = null,

    @SerializedName("createdAt")
    val createdAt: String,

    @SerializedName("updatedAt")
    val updatedAt: String
)



data class ErrorResponseDto(
    @SerializedName("statusCode")
    val statusCode: Int,

    @SerializedName("message")
    val message: Any,

    @SerializedName("error")
    val error: String,

    @SerializedName("timestamp")
    val timestamp: String,

    @SerializedName("path")
    val path: String,

    @SerializedName("code")
    val code: String? = null
)
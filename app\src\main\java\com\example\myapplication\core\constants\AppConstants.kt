package com.example.myapplication.core.constants

object AppConstants {

    object Api {
        const val BASE_URL = "http://10.0.2.2:3000/"
        const val TIMEOUT = 30
    }

    object Routes {
        const val MAIN = "main"
        const val LOGIN = "login"
        const val REGISTER = "register"
        const val SPLASH = "splash"
    }


    object Preferences {
        const val USER_PREFERENCES = "user_preferences"
        const val IS_LOGGED_IN = "is_logged_in"
        const val ACCESS_TOKEN = "access_token"
        const val REFRESH_TOKEN = "refresh_token"
    }

    object Validation {
        const val MIN_PASSWORD_LENGTH = 6
        const val MIN_USERNAME_LENGTH = 3
    }
}

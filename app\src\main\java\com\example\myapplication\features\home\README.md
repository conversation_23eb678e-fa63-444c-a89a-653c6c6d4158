# Home Module - Navegação e Tela de Detalhes

Este módulo implementa a tela principal do aplicativo com navegação entre lista de obras e detalhes de obras individuais.

## Funcionalidades Implementadas

### 1. Navegação Interna
- **Lista de Obras**: Tela principal mostrando todas as obras
- **Detalhes da Obra**: Tela detalhada de uma obra específica
- **Navegação Fluida**: Transição suave entre as telas

### 2. Tela de Detalhes da Obra
- **Informações Completas**: Título, autor, artista, descrição, etc.
- **Imagem de Capa**: Exibição da capa da obra
- **Estatísticas**: Avaliação, número de reviews, capítulos
- **Ações**: Editar, deletar, adicionar capítulos, gerenciar galeria

### 3. Integração com Works Module
- **ViewModels**: Utiliza os ViewModels do módulo works
- **Use Cases**: Integração com todos os use cases implementados
- **Estados**: Gerenciamento de loading, error e success

## Estrutura da Navegação

### Estados de Navegação
```kotlin
sealed class HomeNavigationState {
    object WorkList : HomeNavigationState()
    data class WorkDetail(val workId: String) : HomeNavigationState()
    data class EditWork(val work: WorkDto) : HomeNavigationState()
}
```

### Fluxo de Navegação
1. **Lista de Obras** → Clique em uma obra → **Detalhes da Obra**
2. **Detalhes da Obra** → Botão voltar → **Lista de Obras**
3. **Detalhes da Obra** → Botão editar → **Tela de Edição** (em desenvolvimento)

## Componentes Principais

### HomeScreen
- **Responsabilidade**: Gerenciar navegação principal e bottom navigation
- **Estados**: Controla qual tela está ativa (Dashboard, Profile, Settings)
- **Navegação**: Gerencia transições entre telas

### DashboardContent
- **Responsabilidade**: Gerenciar navegação interna do dashboard
- **Estados**: Controla navegação entre lista e detalhes
- **ViewModels**: Instancia ViewModels conforme necessário

### WorkListScreenWithNavigation
- **Responsabilidade**: Lista de obras com navegação
- **Interação**: Cards clicáveis que navegam para detalhes
- **Funcionalidades**: Busca, filtros, adicionar nova obra

### WorkDetailScreen
- **Responsabilidade**: Exibir detalhes completos de uma obra
- **Ações**: Editar, deletar, gerenciar capítulos e imagens
- **Estados**: Loading, error, success para todas as operações

## Como Usar

### Navegação para Detalhes
```kotlin
// Na lista de obras, ao clicar em um card:
onWorkClick = { workId ->
    navigationState = HomeNavigationState.WorkDetail(workId)
}
```

### Voltar para Lista
```kotlin
// Na tela de detalhes, ao clicar em voltar:
onNavigateBack = {
    navigationState = HomeNavigationState.WorkList
}
```

### Operações na Tela de Detalhes
```kotlin
// Carregar obra
viewModel.onEvent(WorkDetailUiEvent.LoadWork(workId))

// Deletar obra
viewModel.onEvent(WorkDetailUiEvent.DeleteWork(workId))

// Criar capítulo
val chapterDto = CreateChapterDto(...)
viewModel.onEvent(WorkDetailUiEvent.CreateChapter(chapterDto))
```

## Interface da Tela de Detalhes

### Seções Principais
1. **Header**: Título, botões de ação (editar, deletar)
2. **Capa e Info Básica**: Imagem, título, autor, artista, tipo, status
3. **Estatísticas**: Avaliação, reviews, capítulos
4. **Descrição**: Texto completo da descrição
5. **Ações**: Botões para adicionar capítulo e gerenciar galeria
6. **Info Adicional**: Datas de criação e atualização

### Estados da Interface
- **Loading**: Indicador de carregamento centralizado
- **Error**: Mensagem de erro com botão de retry
- **Success**: Conteúdo completo da obra
- **Operations**: Feedback para operações (editar, deletar, etc.)

## Funcionalidades Futuras

### Em Desenvolvimento
1. **Tela de Edição**: Formulário para editar dados da obra
2. **Gerenciamento de Capítulos**: Lista e edição de capítulos
3. **Galeria de Imagens**: Visualização e gerenciamento de imagens
4. **Filtros Avançados**: Filtros mais específicos na lista
5. **Busca**: Funcionalidade de busca por título, autor, etc.

### Melhorias Planejadas
1. **Cache**: Cache local para melhor performance
2. **Offline**: Suporte para visualização offline
3. **Favoritos**: Sistema de favoritos
4. **Compartilhamento**: Compartilhar obras
5. **Notificações**: Notificações de novos capítulos

## Arquitetura

### Princípios Seguidos
- **SOLID**: Responsabilidade única, extensibilidade
- **Clean Architecture**: Separação clara de camadas
- **Composable Functions**: Componentes reutilizáveis
- **State Management**: Estados bem definidos
- **Navigation**: Navegação declarativa

### Padrões Utilizados
- **MVVM**: Model-View-ViewModel
- **Repository Pattern**: Abstração de dados
- **Use Case Pattern**: Lógica de negócio isolada
- **Observer Pattern**: Reatividade com Flow/StateFlow

## Testes

### Testes Recomendados
1. **Unit Tests**: ViewModels e Use Cases
2. **Integration Tests**: Navegação entre telas
3. **UI Tests**: Interações do usuário
4. **Screenshot Tests**: Consistência visual

### Exemplo de Teste
```kotlin
@Test
fun `when work is loaded, should display work details`() {
    // Given
    val workId = "test-work-id"
    val expectedWork = WorkDto(...)
    
    // When
    viewModel.onEvent(WorkDetailUiEvent.LoadWork(workId))
    
    // Then
    assertEquals(expectedWork, viewModel.uiState.value.work)
}
```

## Conclusão

A integração entre o módulo home e works está completa, proporcionando uma experiência de navegação fluida e funcionalidades completas para gerenciamento de obras. A arquitetura permite fácil extensão e manutenção do código.

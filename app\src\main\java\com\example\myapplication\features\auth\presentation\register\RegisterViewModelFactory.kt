package com.example.myapplication.features.auth.presentation.register

import AppC<PERSON>rDI
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.features.auth.domain.useCase.RegisterUseCase


class RegisterViewModelFactory(
    private val appContainer: AppContainerDI
): ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(RegisterViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return RegisterViewModel(
                registerUseCase = RegisterUseCase(
                    authRepository = appContainer.authRepository
                ),
                authenticationManager = appContainer.authenticationManager
            ) as T
        }
        throw IllegalArgumentException("Classe ViewModel desconhecida: ${modelClass.name}")
    }
}
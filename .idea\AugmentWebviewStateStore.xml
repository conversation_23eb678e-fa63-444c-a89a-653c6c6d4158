<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;69d051d3-0210-4ef5-9b53-217b750b37be&quot;,&quot;conversations&quot;:{&quot;fa6f87d1-6b42-4523-9263-1eaf3852c361&quot;:{&quot;id&quot;:&quot;fa6f87d1-6b42-4523-9263-1eaf3852c361&quot;,&quot;createdAtIso&quot;:&quot;2025-06-20T19:23:30.616Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-20T19:23:30.616Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;69d051d3-0210-4ef5-9b53-217b750b37be&quot;:{&quot;id&quot;:&quot;69d051d3-0210-4ef5-9b53-217b750b37be&quot;,&quot;createdAtIso&quot;:&quot;2025-06-20T19:23:30.770Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-20T19:23:30.770Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;09ee5e83-ee22-4a0b-8469-caf33c49eb34&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>
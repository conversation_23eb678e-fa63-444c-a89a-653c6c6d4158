package com.example.myapplication.core.common

// state de retorno para padronizar respostas de operações asíncronas
sealed class ResultAsyncState<T>(
    val data: T? = null,
    val message: String? = null
) {
    class Success<T>(data: T) : ResultAsyncState<T>(data)
    class Error<T>(message: String, data: T? = null) : ResultAsyncState<T>(data, message)
    class Loading<T> : ResultAsyncState<T>()
}


// ui para carrgamento
data class UiState<T>(
    val isLoading: Boolean = false,
    val data: T? = null,
    val error: String? = null
)


/**
 * Interface para eventos da UI
 */
interface UiEvent

/**
 * Interface para efeitos da UI (navegação, toasts, etc.)
 */
interface UiEffect


interface PaginatedData {
    val total: Int
    val page: Int
    val limit: Int
    val totalPages: Int
}


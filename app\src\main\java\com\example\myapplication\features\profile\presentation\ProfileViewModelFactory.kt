package com.example.myapplication.features.profile.presentation

import AppContainerDI
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.features.auth.domain.useCase.GetProfileUseCase

class ProfileViewModelFactory(
    private val appContainer: AppContainerDI
): ViewModelProvider.Factory {
    override fun <T : androidx.lifecycle.ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(ProfileViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return ProfileViewModel(
               getUserProfileUseCase = GetProfileUseCase(
                    authRepository = appContainer.authRepository
               )
            ) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
    }
}
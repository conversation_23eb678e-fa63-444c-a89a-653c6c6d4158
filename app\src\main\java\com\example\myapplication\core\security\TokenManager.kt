package com.example.myapplication.core.security

import android.annotation.SuppressLint
import android.content.Context
import com.example.myapplication.features.auth.domain.model.AuthToken
import com.example.myapplication.features.auth.domain.model.UserLogin
import kotlinx.coroutines.flow.StateFlow

import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.example.myapplication.core.constants.AppConstants
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import androidx.core.content.edit

// gerenciar tokens da aplicação


interface ITokenManagerService {
    val isAuthenticated: StateFlow<Boolean>
    val currentUser: StateFlow<UserLogin?>

    suspend fun saveTokens(authToken: AuthToken, user: UserLogin)
    suspend fun getAccessToken(): String?
    suspend fun getRefreshToken(): String?
    suspend fun clearTokens()
    suspend fun isTokenValid(): <PERSON><PERSON><PERSON>
    suspend fun updateUser(user: UserLogin)
    suspend fun getTokenExpirationInfo(): Pair<Long, Long>?
}

class TokenManager private constructor(
    private val context: Context
) : ITokenManagerService {
    companion object {
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var INSTANCE: TokenManager? = null

        fun getInstance(context: Context): TokenManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TokenManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val encryptedPrefs: SharedPreferences by lazy {
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()

        EncryptedSharedPreferences.create(
            context,
            AppConstants.Preferences.USER_PREFERENCES,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }


    private val _isAuthenticated = MutableStateFlow(false)
    override val isAuthenticated: StateFlow<Boolean> = _isAuthenticated.asStateFlow()

    private val _currentUser = MutableStateFlow<UserLogin?>(null)
    override val currentUser: StateFlow<UserLogin?> = _currentUser.asStateFlow()

    init {
        checkAuthenticationStatus()
    }


    override suspend fun saveTokens(authToken: AuthToken, user: UserLogin) {
        try {
            encryptedPrefs.edit().apply {
                putString(AppConstants.Preferences.ACCESS_TOKEN, authToken.accessToken)
                putString(AppConstants.Preferences.REFRESH_TOKEN, authToken.refreshToken)
                putString("token_type", authToken.tokenType)
                putLong("expires_in", authToken.expiresIn)
                putLong("token_saved_at", System.currentTimeMillis())
                putBoolean(AppConstants.Preferences.IS_LOGGED_IN, true)

                // Salvar dados do usuário
                putLong("user_id", user.id)
                putString("user_username", user.username)
                putString("user_email", user.email)
                putString("user_full_name", user.fullName)
                putString("user_avatar", user.avatar)
                putBoolean("user_is_active", user.isActive)
                putString("user_role", user.role)
                putString("user_created_at", user.createdAt)
                putString("user_updated_at", user.updatedAt)

                apply()
            }

            _isAuthenticated.value = true
            _currentUser.value = user
        } catch (e: Exception) {
            println("Erro ao salvar tokens: ${e.message}")
            throw SecurityException("Falha ao salvar tokens: ${e.message}", e)
        }
    }


    override suspend fun getAccessToken(): String? {
        return if (isTokenValid()) {
            encryptedPrefs.getString(AppConstants.Preferences.ACCESS_TOKEN, null)
        } else {
            null
        }
    }

    override suspend fun getRefreshToken(): String? {
        return encryptedPrefs.getString(AppConstants.Preferences.REFRESH_TOKEN, null)
    }

    override suspend fun isTokenValid(): Boolean {
        val isLoggedIn = encryptedPrefs.getBoolean(AppConstants.Preferences.IS_LOGGED_IN, false)
        if (!isLoggedIn) {
            return false
        }

        val tokenSavedAt = encryptedPrefs.getLong("token_saved_at", 0)
        val expiresIn = encryptedPrefs.getLong("expires_in", 0)

        if (tokenSavedAt == 0L || expiresIn == 0L) {
            return false
        }

        val currentTime = System.currentTimeMillis()
        val tokenExpirationTime = tokenSavedAt + (expiresIn * 1000)

        val bufferTime = 5 * 60 * 1000
        val isValid = currentTime < (tokenExpirationTime - bufferTime)

        if (!isValid) {
            println(
                "Token expirado ou inválido. Token salvo em: $tokenSavedAt, Expira em: $tokenExpirationTime, Tempo atual: $currentTime"
            )
        } else {
            val timeToExpire = (tokenExpirationTime - currentTime) / 1000 / 60
            println("Token válido. Tempo restante para expirar: $timeToExpire minutos")
        }

        return isValid
    }

    override suspend fun clearTokens() {
        val currentUsername = _currentUser.value?.username
        println("Logout realizado para usuário: ${currentUsername ?: "desconhecido"}")

        encryptedPrefs.edit { clear() }
        _currentUser.value = null
        _isAuthenticated.value = false
    }

    override suspend fun updateUser(user: UserLogin) {
        if (_isAuthenticated.value) {
            encryptedPrefs.edit().apply {
                putLong("user_id", user.id)
                putString("user_username", user.username)
                putString("user_email", user.email)
                putString("user_full_name", user.fullName)
                putString("user_avatar", user.avatar)
                putBoolean("user_is_active", user.isActive)
                putString("user_role", user.role)
                putString("user_created_at", user.createdAt)
                putString("user_updated_at", user.updatedAt)
                apply()
            }
            _currentUser.value = user
        }
    }

    private fun checkAuthenticationStatus() {
        try {
            val isLoggedIn = encryptedPrefs.getBoolean(AppConstants.Preferences.IS_LOGGED_IN, false)

            if (isLoggedIn) {
                // Restaurar dados do usuário
                val userId = encryptedPrefs.getLong("user_id", 0)
                val username = encryptedPrefs.getString("user_username", "") ?: ""
                val email = encryptedPrefs.getString("user_email", "") ?: ""
                val fullName = encryptedPrefs.getString("user_full_name", "") ?: ""
                val avatar = encryptedPrefs.getString("user_avatar", null)
                val isActive = encryptedPrefs.getBoolean("user_is_active", true)
                val role = encryptedPrefs.getString("user_role", "") ?: ""
                val createdAt = encryptedPrefs.getString("user_created_at", "") ?: ""
                val updatedAt = encryptedPrefs.getString("user_updated_at", "") ?: ""

                if (userId > 0 && username.isNotEmpty() && email.isNotEmpty()) {
                    val user = UserLogin(
                        id = userId,
                        username = username,
                        email = email,
                        fullName = fullName,
                        avatar = avatar,
                        isActive = isActive,
                        createdAt = createdAt,
                        role = role,
                        updatedAt = updatedAt
                    )

                    _currentUser.value = user
                    _isAuthenticated.value = true

                } else {
                    println("Dados do usuário inválidos ou incompletos. Limpando estado de autenticação.")
                    _isAuthenticated.value = false
                    _currentUser.value = null
                }
            } else {
                println("Usuário não está logado. Limpando estado de autenticação.")
            }
        } catch (e: Exception) {
            println("Erro ao verificar status de autenticação: ${e.message}")
            _isAuthenticated.value = false
            _currentUser.value = null
        }
    }


    override suspend fun getTokenExpirationInfo(): Pair<Long, Long>? {
        return try {
            val tokenSavedAt = encryptedPrefs.getLong("token_saved_at", 0)
            val expiresIn = encryptedPrefs.getLong("expires_in", 0)

            if (tokenSavedAt == 0L || expiresIn == 0L) {
                null
            } else {
                Pair(tokenSavedAt, expiresIn)
            }
        } catch (e: Exception) {
            println("Erro ao obter informações de expiração do token: ${e.message}")
            null
        }
    }
}
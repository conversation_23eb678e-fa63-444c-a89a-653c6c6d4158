package com.example.myapplication.features.works.domain.useCase

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.dto.SetCoverImageDto
import com.example.myapplication.features.works.domain.repository.WorksRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class SetCoverImageUseCase(
    private val worksRepository: WorksRepository
) {
    operator fun invoke(id: String, setCoverImageRequest: SetCoverImageDto): Flow<ResultAsyncState<Unit>> = flow {
        try {
            emit(ResultAsyncState.Loading())
            val response = worksRepository.setCoverImage(id, setCoverImageRequest)
            emit(response)
        } catch (e: Exception) {
            emit(ResultAsyncState.Error("Erro ao definir imagem de capa: ${e.message}"))
        }
    }
}

class RemoveCoverImageUseCase(
    private val worksRepository: WorksRepository
) {
    operator fun invoke(id: String): Flow<ResultAsyncState<Unit>> = flow {
        try {
            emit(ResultAsyncState.Loading())
            val response = worksRepository.removeCoverImage(id)
            emit(response)
        } catch (e: Exception) {
            emit(ResultAsyncState.Error("Erro ao remover imagem de capa: ${e.message}"))
        }
    }
}

package com.example.myapplication.features.auth.domain.useCase

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.auth.domain.model.LoginRequest
import com.example.myapplication.features.auth.domain.model.LoginResponse
import com.example.myapplication.features.auth.domain.repository.AuthRepository

class LoginUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(usernameOrEmail: String, password: String): ResultAsyncState<LoginResponse> {
        when {
            usernameOrEmail.isBlank() -> return ResultAsyncState.Error("O Nome de usuário ou e-mail não pode estar vazio.")
            password.isBlank() -> return ResultAsyncState.Error("A senha não pode estar vazia.")
        }

        val loginRequest = LoginRequest(
            usernameOrEmail = usernameOrEmail,
            password = password
        )

        return authRepository.login(loginRequest)
    }

}


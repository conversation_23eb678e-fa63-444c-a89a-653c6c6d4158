package com.example.myapplication.data.remote.dto

import com.google.gson.annotations.SerializedName


data class BaseResponseDto<T>(
    @SerializedName("success")
    val success: <PERSON><PERSON>an,

    @SerializedName("message")
    val message: String? = null,

    @SerializedName("data")
    val data: T? = null,

    @SerializedName("error_code")
    val errorCode: String? = null,

    @SerializedName("meta")
    val meta: MetaDto? = null
)

data class MetaDto(
    @SerializedName("timestamp")
    val timestamp: String
)
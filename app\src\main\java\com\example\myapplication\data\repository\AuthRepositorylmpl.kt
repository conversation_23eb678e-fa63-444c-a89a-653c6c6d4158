package com.example.myapplication.data.repository

import com.example.myapplication.core.common.ResultAsyncState

import com.example.myapplication.features.auth.data.remote.api.AuthApiService
import com.example.myapplication.features.auth.data.remote.dto.login.ErrorResponseDto
import com.example.myapplication.features.auth.data.remote.dto.login.LoginRequestDto
import com.example.myapplication.features.auth.data.remote.dto.login.LoginResponseDto
import com.example.myapplication.features.auth.data.remote.dto.profile.ProfileResponseDto
import com.example.myapplication.features.auth.data.remote.dto.register.RegisterRequestDto
import com.example.myapplication.features.auth.domain.model.AuthError
import com.example.myapplication.features.auth.domain.model.LoginRequest
import com.example.myapplication.features.auth.domain.model.LoginResponse
import com.example.myapplication.features.auth.domain.model.RegisterRequest
import com.example.myapplication.features.auth.domain.model.RegisterResponse
import com.example.myapplication.features.auth.domain.model.UserLogin
import com.example.myapplication.features.auth.domain.repository.AuthRepository
import com.google.gson.Gson
import retrofit2.HttpException
import java.io.IOException

class AuthRepositorylmpl(
    private val authService: AuthApiService,
): AuthRepository {

    override suspend fun login(loginRequest: LoginRequest): ResultAsyncState<LoginResponse> {
        return try {
            val requestDto = LoginRequestDto(
                usernameOrEmail = loginRequest.usernameOrEmail,
                password = loginRequest.password
            )

            val response = authService.login(requestDto)
            if (response.isSuccessful) handleSuccessResponse(response.body())
            else handleErrorResponse(response.errorBody()?.string(), response.code())
        } catch (e: HttpException) {
            ResultAsyncState.Error("Erro de rede: ${e.message()}")
        } catch (e: IOException) {

            println("Erro de IO: ${e.message}")

            ResultAsyncState.Error("Erro de conexão: Verifique sua internet")
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro inesperado: ${e.message}")
        }
    }


    private fun handleSuccessResponse(baseResponse: Any?): ResultAsyncState<LoginResponse> {
        val response = baseResponse as? com.example.myapplication.data.remote.dto.BaseResponseDto<*>
            ?: return ResultAsyncState.Error("Resposta vazia do servidor")
        if (!response.success || response.data == null)  return ResultAsyncState.Error(response.message ?: "Operação não foi bem-sucedida")
        val loginResponseDto = response.data as? LoginResponseDto
            ?: return ResultAsyncState.Error("Resposta de login inválida do servidor")
        val userDto = loginResponseDto.user
            ?: return ResultAsyncState.Error("Dados do usuário não encontrados na resposta")
        val loginResponse = LoginResponse(
            accessToken = loginResponseDto.accessToken,
            refreshToken = loginResponseDto.refreshToken,
            tokenType = loginResponseDto.tokenType,
            expiresIn = loginResponseDto.expiresIn,
            user = UserLogin(
                id = userDto.id,
                username = userDto.username,
                email = userDto.email,
                fullName = userDto.fullName,
                avatar = userDto.avatar,
                isActive = userDto.isActive,
                role = userDto.role.toString(),
                createdAt = userDto.createdAt,
                updatedAt = userDto.updatedAt
            )
        )
        return ResultAsyncState.Success(loginResponse)
    }

    private fun handleErrorResponse(errorBody: String?, statusCode: Int): ResultAsyncState<LoginResponse> {
        val authError = try {
            val errorResponse = Gson().fromJson(errorBody, ErrorResponseDto::class.java)
            val message = when (errorResponse.message) {
                is String -> errorResponse.message
                is List<*> -> errorResponse.message.joinToString(", ")
                else -> "Erro desconhecido"
            }
            AuthError(
                statusCode = errorResponse.statusCode,
                message = message,
                error = errorResponse.error,
                code = errorResponse.code
            )
        } catch (e: Exception) {
            AuthError(
                statusCode = statusCode,
                message = "Erro de autenticação",
                error = "Bad Request"
            )
        }
        return ResultAsyncState.Error(authError.message)
    }

    override suspend fun register(registerRequest: RegisterRequest): ResultAsyncState<RegisterResponse> {
        return try {
            val requestDto = RegisterRequestDto(
                username = registerRequest.username,
                email = registerRequest.email,
                password = registerRequest.password,
                fullName = registerRequest.fullName
            )
            val response = authService.register(requestDto)
            if (response.isSuccessful) handleRegisterSuccessResponse(response.body())
            else handleRegisterErrorResponse(response.errorBody()?.string(), response.code())

        } catch (e: HttpException) {
            ResultAsyncState.Error("Erro de rede: ${e.message()}")
        } catch (e: IOException) {
            ResultAsyncState.Error("Erro de conexão: Verifique sua internet")
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro inesperado: ${e.message}")
        }
    }

    private fun handleRegisterSuccessResponse(baseResponse: Any?): ResultAsyncState<RegisterResponse> {
        val response = baseResponse as? com.example.myapplication.data.remote.dto.BaseResponseDto<*>
            ?: return ResultAsyncState.Error("Resposta vazia do servidor")
        if (!response.success || response.data == null) return ResultAsyncState.Error(response.message ?: "Operação não foi bem-sucedida")
        val loginResponseDto = response.data as LoginResponseDto
        val userDto = loginResponseDto.user
            ?: return ResultAsyncState.Error("Dados do usuário não encontrados na resposta")
        val registerResponse = RegisterResponse(
            accessToken = loginResponseDto.accessToken,
            refreshToken = loginResponseDto.refreshToken,
            tokenType = loginResponseDto.tokenType,
            expiresIn = loginResponseDto.expiresIn,
            user = UserLogin(
                id = userDto.id,
                username = userDto.username,
                email = userDto.email,
                fullName = userDto.fullName,
                avatar = userDto.avatar,
                isActive = userDto.isActive,
                role = userDto.role.toString(),
                createdAt = userDto.createdAt,
                updatedAt = userDto.updatedAt
            )
        )
        return ResultAsyncState.Success(registerResponse)
    }

    private fun handleRegisterErrorResponse(errorBody: String?, statusCode: Int): ResultAsyncState<RegisterResponse> {
        val authError = try {
            val errorResponse = Gson().fromJson(errorBody, ErrorResponseDto::class.java)
            val message = when (errorResponse.message) {
                is String -> errorResponse.message
                is List<*> -> (errorResponse.message as List<String>).joinToString(", ")
                else -> "Erro desconhecido"
            }
            AuthError(
                statusCode = errorResponse.statusCode,
                message = message,
                error = errorResponse.error,
                code = errorResponse.code
            )
        } catch (e: Exception) {
            AuthError(
                statusCode = statusCode,
                message = "Erro de registro",
                error = "Bad Request"
            )
        }
        return ResultAsyncState.Error(authError.message)
    }


   override suspend fun getUserProfile(): ResultAsyncState<ProfileResponseDto> {
        return try {
            val response = authService.getUserProfile()
            if (response.isSuccessful) {
                val profileResponse = response.body()
                if (profileResponse != null) {
                    ResultAsyncState.Success(profileResponse.data)
                } else {
                    ResultAsyncState.Error("Resposta vazia do servidor")
                }
            } else {
                handleErrorResponse(response.errorBody()?.string(), response.code())
            }
        } catch (e: HttpException) {
            ResultAsyncState.Error("Erro de rede: ${e.message()}")
        } catch (e: IOException) {
            ResultAsyncState.Error("Erro de conexão: Verifique sua internet")
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro inesperado: ${e.message}")
        } as ResultAsyncState<ProfileResponseDto>
    }
}


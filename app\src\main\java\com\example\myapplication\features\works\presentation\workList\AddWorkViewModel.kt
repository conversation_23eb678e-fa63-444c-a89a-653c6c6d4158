package com.example.myapplication.features.works.presentation.workList

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel

class AddWorkViewModel : ViewModel() {
    var showDialog by mutableStateOf(false)
        private set

    fun openDialog() {
        showDialog = true
    }

    fun closeDialog() {
        showDialog = false
    }

    fun addWork() {

        closeDialog()
    }
}


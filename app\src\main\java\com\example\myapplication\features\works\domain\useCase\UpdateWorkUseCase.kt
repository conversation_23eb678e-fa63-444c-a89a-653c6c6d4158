package com.example.myapplication.features.works.domain.useCase

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.dto.UpdateWorkDto
import com.example.myapplication.features.works.data.remote.dto.WorkDto
import com.example.myapplication.features.works.domain.repository.WorksRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class UpdateWorkUseCase(
    private val worksRepository: WorksRepository
) {
    operator fun invoke(id: String, updateWorkRequest: UpdateWorkDto): Flow<ResultAsyncState<WorkDto>> = flow {
        try {
            emit(ResultAsyncState.Loading())
            val response = worksRepository.updateWork(id, updateWorkRequest)
            emit(response)
        } catch (e: Exception) {
            emit(ResultAsyncState.Error("Erro ao atualizar obra: ${e.message}"))
        }
    }
}

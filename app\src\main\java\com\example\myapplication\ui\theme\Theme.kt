package com.example.myapplication.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext

private val DarkColorScheme = darkColorScheme(
    primary = PrimaryDark,
    onPrimary = NeutralLight,
    primaryContainer = PrimaryLight,
    onPrimaryContainer = NeutralLight,
    secondary = Secondary,
    onSecondary = NeutralLight,
    secondaryContainer = SecondaryDark,
    onSecondaryContainer = NeutralLight,
    tertiary = AccentRed,
    onTertiary = NeutralLight,
    background = NeutralDarkVariant,
    onBackground = NeutralLight,
    surface = NeutralDark,
    onSurface = NeutralLight,
    error = AccentRed,
    onError = NeutralLight,
)

private val LightColorScheme = lightColorScheme(
    primary = PrimaryDark,
    onPrimary = NeutralLightVariant,
    primaryContainer = PrimaryLight,
    onPrimaryContainer = NeutralDarkVariant,
    secondary = Secondary,
    onSecondary = NeutralLightVariant,
    secondaryContainer = SecondaryDark,
    onSecondaryContainer = NeutralLightVariant,
    tertiary = AccentRed,
    onTertiary = NeutralLightVariant,
    background = NeutralLightVariant,
    onBackground = NeutralDark,
    surface = NeutralLight,
    onSurface = NeutralDark,
    error = AccentRed,
    onError = NeutralLightVariant,
)

@Composable
fun MyApplicationTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false, // Desativado por padrão para manter nossa paleta personalizada
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
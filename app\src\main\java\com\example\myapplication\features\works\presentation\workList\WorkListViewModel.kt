package com.example.myapplication.features.works.presentation.workList

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.dto.GetWorksDto
import com.example.myapplication.features.works.domain.useCase.GetWorksUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

data class  WorkListUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val works: ResultAsyncState.Success<GetWorksDto> = ResultAsyncState.Success(
        GetWorksDto(
            works = emptyList(),
            page = 1,
            limit = 10,
            totalPages = 1,
            total = 1
        )
    )
)

sealed class WorkListUiEvent {
    data object LoadWorks : WorkListUiEvent()
    data object ClearError : WorkListUiEvent()
}

class WorkListViewModel(
    private val getWorksUseCase: GetWorksUseCase
) : ViewModel() {
    private val _uiState = MutableStateFlow(WorkListUiState())
    val uiState = _uiState

    fun onEvent(event: WorkListUiEvent) {
        when (event) {
            is WorkListUiEvent.LoadWorks -> {
                loadWorks()
            }
            is WorkListUiEvent.ClearError -> {
                _uiState.value = _uiState.value.copy(error = null)
            }
        }
    }

    private fun loadWorks() {
        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
        viewModelScope.launch {
            getWorksUseCase().collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isLoading = true)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            works = result,
                            error = null
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }
}
package com.example.myapplication.features.works.domain.repository

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.api.WorkApiService
import com.example.myapplication.features.works.data.remote.dto.*

class WorksRepositopyImpl(
    private val worksService: WorkApiService
): WorksRepository {

    override suspend fun createWork(createWorkRequest: CreateWorkDto): ResultAsyncState<WorkDto> {
        try {
            val response = worksService.createWork(createWorkRequest)
            if (!response.isSuccessful) {
                throw Exception("Erro ao criar obra: ${response.message()}")
            }
        } catch (e: Exception) {
            throw Exception("Erro ao criar obra: ${e.message}")
        }
        return ResultAsyncState.Error( "Erro ao criar obra")
    }

    private fun GetWorksParametersDto?.toQueryMap(): Map<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        this?.let {
            type?.let { map["type"] = it.value }
            status?.let { map["status"] = it.value }
            author?.let { map["author"] = it }
            artist?.let { map["artist"] = it }
            search?.let { map["search"] = it }
            tags?.let { map["tags"] = it.joinToString(",") }
            minRating?.let { map["minRating"] = it }
            maxRating?.let { map["maxRating"] = it }
            page?.let { map["page"] = it }
            limit?.let { map["limit"] = it }
            sortBy?.let { map["sortBy"] = it.value }
            sortOrder?.let { map["sortOrder"] = it.value }
        }
        return map
    }

    override suspend fun getWorks(params: GetWorksParametersDto?): ResultAsyncState<GetWorksDto> {
        return try {
            val queryMap = params?.toQueryMap() ?: emptyMap()
            val response = worksService.getWorks(queryMap)
            if (response.isSuccessful) {
                val works = response.body()?.data
                ResultAsyncState.Success(
                    works ?: GetWorksDto(
                        works = emptyList(),
                        page = 1,
                        limit = 10,
                        totalPages = 1,
                        total = 1
                    )
                )
            } else {
                ResultAsyncState.Error("Erro ao buscar obras: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao buscar obras: ${e.message}")
        }
    }

    override suspend fun updateWork(id: String, updateWorkRequest: UpdateWorkDto): ResultAsyncState<WorkDto> {
        return try {
            val response = worksService.updateWork(id, updateWorkRequest)
            if (response.isSuccessful) {
                val work = response.body()?.data
                if (work != null) {
                    ResultAsyncState.Success(work)
                } else {
                    ResultAsyncState.Error("Resposta vazia ao atualizar obra")
                }
            } else {
                ResultAsyncState.Error("Erro ao atualizar obra: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao atualizar obra: ${e.message}")
        }
    }

    override suspend fun getWorkById(id: String): ResultAsyncState<WorkDto> {
        return try {
            val response = worksService.getWorkById(id)
            if (response.isSuccessful) {
                val work = response.body()?.data
                if (work != null) {
                    ResultAsyncState.Success(work)
                } else {
                    ResultAsyncState.Error("Obra não encontrada")
                }
            } else {
                ResultAsyncState.Error("Erro ao buscar obra: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao buscar obra: ${e.message}")
        }
    }

    override suspend fun deleteWork(id: String): ResultAsyncState<Unit> {
        return try {
            val response = worksService.deleteWork(id)
            if (response.isSuccessful) {
                ResultAsyncState.Success(Unit)
            } else {
                ResultAsyncState.Error("Erro ao deletar obra: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao deletar obra: ${e.message}")
        }
    }

    override suspend fun createChapter(createChapterRequest: CreateChapterDto): ResultAsyncState<ChapterDto> {
        return try {
            val response = worksService.createChapter(createChapterRequest)
            if (response.isSuccessful) {
                val chapter = response.body()?.data
                if (chapter != null) {
                    ResultAsyncState.Success(chapter)
                } else {
                    ResultAsyncState.Error("Resposta vazia ao criar capítulo")
                }
            } else {
                ResultAsyncState.Error("Erro ao criar capítulo: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao criar capítulo: ${e.message}")
        }
    }

    override suspend fun setCoverImage(id: String, setCoverImageRequest: SetCoverImageDto): ResultAsyncState<Unit> {
        return try {
            val response = worksService.setCoverImage(id, setCoverImageRequest)
            if (response.isSuccessful) {
                ResultAsyncState.Success(Unit)
            } else {
                ResultAsyncState.Error("Erro ao definir imagem de capa: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao definir imagem de capa: ${e.message}")
        }
    }

    override suspend fun removeCoverImage(id: String): ResultAsyncState<Unit> {
        return try {
            val response = worksService.removeCoverImage(id)
            if (response.isSuccessful) {
                ResultAsyncState.Success(Unit)
            } else {
                ResultAsyncState.Error("Erro ao remover imagem de capa: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao remover imagem de capa: ${e.message}")
        }
    }

    override suspend fun addGalleryImages(id: String, addGalleryImagesRequest: AddGalleryImagesDto): ResultAsyncState<Unit> {
        return try {
            val response = worksService.addGalleryImages(id, addGalleryImagesRequest)
            if (response.isSuccessful) {
                ResultAsyncState.Success(Unit)
            } else {
                ResultAsyncState.Error("Erro ao adicionar imagens à galeria: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao adicionar imagens à galeria: ${e.message}")
        }
    }

    override suspend fun removeGalleryImage(id: String, imageId: String): ResultAsyncState<Unit> {
        return try {
            val response = worksService.removeGalleryImage(id, imageId)
            if (response.isSuccessful) {
                ResultAsyncState.Success(Unit)
            } else {
                ResultAsyncState.Error("Erro ao remover imagem da galeria: ${response.message()}")
            }
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao remover imagem da galeria: ${e.message}")
        }
    }
}
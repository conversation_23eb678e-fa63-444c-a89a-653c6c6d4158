package com.example.myapplication.features.works.domain.repository

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.dto.*
import com.example.myapplication.features.works.domain.model.*

interface WorksRepository {
    suspend fun createWork(createWorkRequest: CreateWorkDto): ResultAsyncState<WorkDto>
    suspend fun getWorks(params: GetWorksParametersDto? = null): ResultAsyncState<GetWorksDto>
    suspend fun updateWork(id: String, updateWorkRequest: UpdateWorkDto): ResultAsyncState<WorkDto>
    suspend fun getWorkById(id: String): ResultAsyncState<WorkDto>
    suspend fun deleteWork(id: String): ResultAsyncState<Unit>
    suspend fun createChapter(createChapterRequest: CreateChapterDto): ResultAsyncState<ChapterDto>
    suspend fun setCoverImage(id: String, setCoverImageRequest: SetCoverImageDto): ResultAsyncState<Unit>
    suspend fun removeCoverImage(id: String): ResultAsyncState<Unit>
    suspend fun addGalleryImages(id: String, addGalleryImagesRequest: AddGalleryImagesDto): ResultAsyncState<Unit>
    suspend fun removeGalleryImage(id: String, imageId: String): ResultAsyncState<Unit>
}

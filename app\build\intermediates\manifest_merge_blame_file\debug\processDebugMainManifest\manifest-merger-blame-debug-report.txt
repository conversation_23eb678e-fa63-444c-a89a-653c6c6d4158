1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:5:22-64
12
13    <permission
13-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2e49a2f8451d49127ce4bb9ca1dce0d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
14        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2e49a2f8451d49127ce4bb9ca1dce0d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
15        android:protectionLevel="signature" />
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2e49a2f8451d49127ce4bb9ca1dce0d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
16
17    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2e49a2f8451d49127ce4bb9ca1dce0d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
17-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2e49a2f8451d49127ce4bb9ca1dce0d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
18
19    <application
19-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:7:5-29:19
20        android:allowBackup="true"
20-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:8:9-35
21        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
21-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2e49a2f8451d49127ce4bb9ca1dce0d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
22        android:dataExtractionRules="@xml/data_extraction_rules"
22-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:9:9-65
23        android:debuggable="true"
24        android:extractNativeLibs="false"
25        android:fullBackupContent="@xml/backup_rules"
25-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:10:9-54
26        android:icon="@mipmap/ic_launcher"
26-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:11:9-43
27        android:label="@string/app_name"
27-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:12:9-41
28        android:networkSecurityConfig="@xml/network_security_config"
28-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:17:9-69
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:13:9-54
30        android:supportsRtl="true"
30-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:14:9-35
31        android:testOnly="true"
32        android:theme="@style/Theme.MyApplication" >
32-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:15:9-51
33        <activity
33-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:18:9-28:20
34            android:name="com.example.myapplication.MainActivity"
34-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:19:13-41
35            android:exported="true"
35-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:20:13-36
36            android:label="@string/app_name"
36-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:21:13-45
37            android:theme="@style/Theme.MyApplication" >
37-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:22:13-55
38            <intent-filter>
38-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:23:13-27:29
39                <action android:name="android.intent.action.MAIN" />
39-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:24:17-69
39-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:24:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:26:17-77
41-->C:\Users\<USER>\AndroidStudioProjects\MyApplication2\app\src\main\AndroidManifest.xml:26:27-74
42            </intent-filter>
43        </activity>
44        <activity
44-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3e439b8de80b6f53024c0dbd40a3460\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
45            android:name="androidx.compose.ui.tooling.PreviewActivity"
45-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3e439b8de80b6f53024c0dbd40a3460\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
46            android:exported="true" />
46-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3e439b8de80b6f53024c0dbd40a3460\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
47
48        <provider
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4edbb9c03b831e4bea9fee0fa0a0ace3\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
49            android:name="androidx.startup.InitializationProvider"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4edbb9c03b831e4bea9fee0fa0a0ace3\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
50            android:authorities="com.example.myapplication.androidx-startup"
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4edbb9c03b831e4bea9fee0fa0a0ace3\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
51            android:exported="false" >
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4edbb9c03b831e4bea9fee0fa0a0ace3\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
52            <meta-data
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4edbb9c03b831e4bea9fee0fa0a0ace3\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.emoji2.text.EmojiCompatInitializer"
53-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4edbb9c03b831e4bea9fee0fa0a0ace3\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
54                android:value="androidx.startup" />
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4edbb9c03b831e4bea9fee0fa0a0ace3\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\041d7628a5a5e1a612824f9347197d96\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
56-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\041d7628a5a5e1a612824f9347197d96\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
57                android:value="androidx.startup" />
57-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\041d7628a5a5e1a612824f9347197d96\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
58            <meta-data
58-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
60                android:value="androidx.startup" />
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
61        </provider>
62
63        <activity
63-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6f239b6a506ba80bba6eb5bb9641c7\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
64            android:name="androidx.activity.ComponentActivity"
64-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6f239b6a506ba80bba6eb5bb9641c7\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
65            android:exported="true" />
65-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6f239b6a506ba80bba6eb5bb9641c7\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
66
67        <receiver
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
68            android:name="androidx.profileinstaller.ProfileInstallReceiver"
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
69            android:directBootAware="false"
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
70            android:enabled="true"
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
71            android:exported="true"
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
72            android:permission="android.permission.DUMP" >
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
74                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
77                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
80                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
83                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\075ad9c29b2da4c077547244b0fdf4b4\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
84            </intent-filter>
85        </receiver>
86    </application>
87
88</manifest>

package com.example.myapplication.features.works.presentation.workDetail

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.core.common.UiEvent
import com.example.myapplication.features.works.data.remote.dto.*
import com.example.myapplication.features.works.domain.useCase.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

data class WorkDetailUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val work: WorkDto? = null,
    val isUpdating: Boolean = false,
    val isDeleting: Boolean = false,
    val operationSuccess: String? = null
)

sealed class WorkDetailUiEvent : UiEvent {
    data class LoadWork(val id: String) : WorkDetailUiEvent()
    data class UpdateWork(val id: String, val updateWorkDto: UpdateWorkDto) : WorkDetailUiEvent()
    data class DeleteWork(val id: String) : WorkDetailUiEvent()
    data class CreateChapter(val createChapterDto: CreateChapterDto) : WorkDetailUiEvent()
    data class SetCoverImage(val id: String, val setCoverImageDto: SetCoverImageDto) : WorkDetailUiEvent()
    data class RemoveCoverImage(val id: String) : WorkDetailUiEvent()
    data class AddGalleryImages(val id: String, val addGalleryImagesDto: AddGalleryImagesDto) : WorkDetailUiEvent()
    data class RemoveGalleryImage(val id: String, val imageId: String) : WorkDetailUiEvent()
    object ClearError : WorkDetailUiEvent()
    object ClearSuccess : WorkDetailUiEvent()
}

class WorkDetailViewModel(
    private val getWorkByIdUseCase: GetWorkByIdUseCase,
    private val updateWorkUseCase: UpdateWorkUseCase,
    private val deleteWorkUseCase: DeleteWorkUseCase,
    private val createChapterUseCase: CreateChapterUseCase,
    private val setCoverImageUseCase: SetCoverImageUseCase,
    private val removeCoverImageUseCase: RemoveCoverImageUseCase,
    private val addGalleryImagesUseCase: AddGalleryImagesUseCase,
    private val removeGalleryImageUseCase: RemoveGalleryImageUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(WorkDetailUiState())
    val uiState: StateFlow<WorkDetailUiState> = _uiState.asStateFlow()

    fun onEvent(event: WorkDetailUiEvent) {
        when (event) {
            is WorkDetailUiEvent.LoadWork -> loadWork(event.id)
            is WorkDetailUiEvent.UpdateWork -> updateWork(event.id, event.updateWorkDto)
            is WorkDetailUiEvent.DeleteWork -> deleteWork(event.id)
            is WorkDetailUiEvent.CreateChapter -> createChapter(event.createChapterDto)
            is WorkDetailUiEvent.SetCoverImage -> setCoverImage(event.id, event.setCoverImageDto)
            is WorkDetailUiEvent.RemoveCoverImage -> removeCoverImage(event.id)
            is WorkDetailUiEvent.AddGalleryImages -> addGalleryImages(event.id, event.addGalleryImagesDto)
            is WorkDetailUiEvent.RemoveGalleryImage -> removeGalleryImage(event.id, event.imageId)
            is WorkDetailUiEvent.ClearError -> clearError()
            is WorkDetailUiEvent.ClearSuccess -> clearSuccess()
        }
    }

    private fun loadWork(id: String) {
        viewModelScope.launch {
            getWorkByIdUseCase(id).collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            work = result.data,
                            error = null
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

    private fun updateWork(id: String, updateWorkDto: UpdateWorkDto) {
        viewModelScope.launch {
            updateWorkUseCase(id, updateWorkDto).collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isUpdating = true, error = null)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isUpdating = false,
                            work = result.data,
                            operationSuccess = "Obra atualizada com sucesso!"
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isUpdating = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

    private fun deleteWork(id: String) {
        viewModelScope.launch {
            deleteWorkUseCase(id).collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isDeleting = true, error = null)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isDeleting = false,
                            operationSuccess = "Obra deletada com sucesso!"
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isDeleting = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

    private fun createChapter(createChapterDto: CreateChapterDto) {
        viewModelScope.launch {
            createChapterUseCase(createChapterDto).collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            operationSuccess = "Capítulo criado com sucesso!"
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

    private fun setCoverImage(id: String, setCoverImageDto: SetCoverImageDto) {
        viewModelScope.launch {
            setCoverImageUseCase(id, setCoverImageDto).collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            operationSuccess = "Imagem de capa definida com sucesso!"
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

    private fun removeCoverImage(id: String) {
        viewModelScope.launch {
            removeCoverImageUseCase(id).collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            operationSuccess = "Imagem de capa removida com sucesso!"
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

    private fun addGalleryImages(id: String, addGalleryImagesDto: AddGalleryImagesDto) {
        viewModelScope.launch {
            addGalleryImagesUseCase(id, addGalleryImagesDto).collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            operationSuccess = "Imagens adicionadas à galeria com sucesso!"
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

    private fun removeGalleryImage(id: String, imageId: String) {
        viewModelScope.launch {
            removeGalleryImageUseCase(id, imageId).collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            operationSuccess = "Imagem removida da galeria com sucesso!"
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

    private fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    private fun clearSuccess() {
        _uiState.value = _uiState.value.copy(operationSuccess = null)
    }
}

package com.example.myapplication.features.works.domain.model

import com.example.myapplication.features.works.domain.enums.*

data class WorkParameters(
    val type: WorkType? = null,
    val status: WorkStatus? = null,
    val author: String? = null,
    val artist: String? = null,
    val search: String? = null,
    val tags: List<String>? = null,
    val minRating: Float? = null,
    val maxRating: Float? = null,
    val page: Int? = 1,
    val limit: Int? = 20,
    val sortBy: WorkSortBy? = null,
    val sortOrder: WorkSortOrder? = null
)


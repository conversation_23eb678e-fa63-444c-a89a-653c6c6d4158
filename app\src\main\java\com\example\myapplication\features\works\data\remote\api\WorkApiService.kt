package com.example.myapplication.features.works.data.remote.api

import com.example.myapplication.data.remote.dto.BaseResponseDto
import com.example.myapplication.features.works.data.remote.dto.*
import retrofit2.Response
import retrofit2.http.*


interface WorkApiService {
    @POST("/works")
    suspend fun createWork(@Body request: CreateWorkDto): Response<BaseResponseDto<WorkDto>>

    @GET("/works")
    suspend fun getWorks(@QueryMap params: Map<String, @JvmSuppressWildcards Any?>? = null): Response<BaseResponseDto<GetWorksDto>>

    @PUT("/works/{id}")
    suspend fun updateWork(@Path("id") id: String, @Body request: UpdateWorkDto): Response<BaseResponseDto<WorkDto>>

    @GET("/works/{id}")
    suspend fun getWorkById(@Path("id") id: String): Response<BaseResponseDto<WorkDto>>

    @DELETE("/works/{id}")
    suspend fun deleteWork(@Path("id") id: String): Response<BaseResponseDto<Unit>>

    @POST("/works/chapters")
    suspend fun createChapter(@Body request: CreateChapterDto): Response<BaseResponseDto<ChapterDto>>

    @POST("/works/{id}/cover-image")
    suspend fun setCoverImage(@Path("id") id: String, @Body request: SetCoverImageDto): Response<BaseResponseDto<Unit>>

    @DELETE("/works/{id}/cover-image")
    suspend fun removeCoverImage(@Path("id") id: String): Response<BaseResponseDto<Unit>>

    @POST("/works/{id}/gallery-images")
    suspend fun addGalleryImages(@Path("id") id: String, @Body request: AddGalleryImagesDto): Response<BaseResponseDto<Unit>>

    @DELETE("/works/{id}/gallery-images/{imageId}")
    suspend fun removeGalleryImage(@Path("id") id: String, @Path("imageId") imageId: String): Response<BaseResponseDto<Unit>>
}

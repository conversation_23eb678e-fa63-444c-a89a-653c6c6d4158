package com.example.myapplication.features.auth.presentation.login

import AppC<PERSON>rDI
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.features.auth.domain.useCase.LoginUseCase

class LoginViewModelFactory(
    private val appContainer: AppContainerDI
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(LoginViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return LoginViewModel(
                loginUseCase = LoginUseCase(
                    authRepository = appContainer.authRepository
                ),
                authenticationManager = appContainer.authenticationManager
            ) as T
        }
        throw IllegalArgumentException("Classe ViewModel desconhecida: ${modelClass.name}")
    }
}


package com.example.myapplication.features.works.domain.enums


enum class WorkType(val value: String) {
    MANHWA("manhwa"),
    MANGA("manga"),
    MANHUA("manhua")
}

enum class WorkStatus(val value: String) {
    ONGOING("ongoing"),
    COMPLETED("completed"),
    HIATU("hiatu"),
    CANCELLED("cancelled")
}

enum class WorkSortBy(val value: String) {
    TITLE("title"),
    RELEASE_DATE("releaseDate"),
    AVERAGE_RATING("averageRating"),
    CREATED_AT("createdAt")
}

enum class WorkSortOrder(val value: String) {
    ASC("asc"),
    DESC("desc")
}


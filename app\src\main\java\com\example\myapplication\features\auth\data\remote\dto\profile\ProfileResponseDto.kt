package com.example.myapplication.features.auth.data.remote.dto.profile


import com.google.gson.annotations.SerializedName

data class ProfileResponseDto(
    @SerializedName("id") val id: Int,
    @SerializedName("username") val username: String,
    @SerializedName("email") val email: String,
    @SerializedName("fullName") val fullName: String,
    @SerializedName("avatar") val avatar: String,
    @SerializedName("isActive") val isActive: Boolean,
    @SerializedName("role") val role: String,
    @SerializedName("createdAt") val createdAt: String,
    @SerializedName("updatedAt") val updatedAt: String,
    @SerializedName("stats") val stats: StatsDto
)

data class StatsDto(
    @SerializedName("readingStats") val readingStats: ReadingStatsDto,
    @SerializedName("totalChaptersRead") val totalChaptersRead: Int,
    @SerializedName("totalLists") val totalLists: Int,
    @SerializedName("totalPublicLists") val totalPublicLists: Int,
    @SerializedName("totalRankings") val totalRankings: Int,
    @SerializedName("totalPublicRankings") val totalPublicRankings: Int,
    @SerializedName("totalReviews") val totalReviews: Int,
    @SerializedName("totalPublicReviews") val totalPublicReviews: Int,
    @SerializedName("averageRating") val averageRating: Double,
    @SerializedName("totalReviewLikes") val totalReviewLikes: Int,
    @SerializedName("averageReadingTime") val averageReadingTime: Double,
    @SerializedName("lastReadingActivity") val lastReadingActivity: String,
    @SerializedName("favoriteGenre") val favoriteGenre: String
)

data class ReadingStatsDto(
    @SerializedName("reading") val reading: Int,
    @SerializedName("completed") val completed: Int,
    @SerializedName("dropped") val dropped: Int,
    @SerializedName("planToRead") val planToRead: Int,
    @SerializedName("onHold") val onHold: Int,
    @SerializedName("totalWorks") val totalWorks: Int
)


#androidx.activity.ComponentActivity6com.example.myapplication.core.common.ResultAsyncStateScreenokhttp3.InterceptorEcom.example.myapplication.core.security.IAuthenticationManagerService<com.example.myapplication.core.security.ITokenManagerServiceHcom.example.myapplication.features.auth.domain.repository.AuthRepositoryGcom.example.myapplication.features.auth.presentation.login.LoginUiEventFcom.example.myapplication.features.auth.presentation.login.LoginResultandroidx.lifecycle.ViewModel,<EMAIL>kotlin.Enum3com.example.myapplication.core.common.PaginatedDataGcom.example.myapplication.features.works.domain.model.WorkPaginatedDataJcom.example.myapplication.features.works.domain.repository.WorksRepositoryNcom.example.myapplication.features.works.presentation.workList.WorkListUiEventHcom.example.myapplication.features.home.presentation.HomeNavigationState-com.example.myapplication.core.common.UiEventRcom.example.myapplication.features.works.presentation.workDetail.WorkDetailUiEvent                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
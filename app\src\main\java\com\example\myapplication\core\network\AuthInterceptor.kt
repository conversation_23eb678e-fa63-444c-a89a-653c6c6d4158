package com.example.myapplication.core.network

import com.example.myapplication.core.security.TokenManager
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import kotlin.jvm.Throws

class AuthInterceptor (
    private val tokenManager: TokenManager
): Interceptor {

    companion object {
        private val OPEN_ENDPOINTS = listOf(
            "/auth/login",
            "/auth/register"
        )
    }

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val requestUrl = originalRequest.url.toString()

        val needsAuth = OPEN_ENDPOINTS.none { endpoint ->
            requestUrl.contains(endpoint)
        }

        if (!needsAuth) {
            return chain.proceed(originalRequest)
        }

        val accessToken = runBlocking {
            try {
                tokenManager.getAccessToken()
            } catch (e: Exception) {
                null
            }
        }

        if (accessToken.isNullOrEmpty()) {
            return chain.proceed(originalRequest)
        }

        val authenticatedRequest = originalRequest.newBuilder()
            .header("Authorization", "Bearer $accessToken")
            .build()

        return chain.proceed(authenticatedRequest)
    }
}

package com.example.myapplication.core.security

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.auth.domain.model.AuthToken
import com.example.myapplication.features.auth.domain.model.LoginResponse
import com.example.myapplication.features.auth.domain.model.RegisterResponse
import com.example.myapplication.features.auth.domain.model.UserLogin
import kotlinx.coroutines.flow.StateFlow

interface IAuthenticationManagerService {
    val isAuthenticated: StateFlow<Boolean>
    val currentUser: StateFlow<UserLogin?>

    suspend fun handleLoginSuccess(loginResponse: LoginResponse): ResultAsyncState<Unit>
    suspend fun handleRegisterSuccess(registerResponse: RegisterResponse): ResultAsyncState<Unit>
    suspend fun logout(): ResultAsyncState<Unit>
    suspend fun checkAuthenticationStatus(): Boolean
}

class AuthenticationManager(
    private val tokenManager: TokenManager,
): IAuthenticationManagerService {
    override val isAuthenticated: StateFlow<Boolean> = tokenManager.isAuthenticated
    override val currentUser: StateFlow<UserLogin?> = tokenManager.currentUser

    override suspend fun handleLoginSuccess(loginResponse: LoginResponse): ResultAsyncState<Unit> {
        return try {
            val authToken = AuthToken(
                accessToken = loginResponse.accessToken,
                refreshToken = loginResponse.refreshToken,
                tokenType = loginResponse.tokenType,
                expiresIn = loginResponse.expiresIn,
            )
            tokenManager.saveTokens(authToken, loginResponse.user)
            ResultAsyncState.Success(Unit)
        } catch (e: Exception) {
            ResultAsyncState.Error("Ocorreu um erro ao processar os dados de login: ${e.message}")
        }
    }

    override suspend fun logout(): ResultAsyncState<Unit> {
        return try {
            tokenManager.clearTokens()
            ResultAsyncState.Success(Unit)
        } catch (e: Exception) {
            ResultAsyncState.Error("Erro ao realizar logout: ${e.message}")
        }
    }

    override suspend fun handleRegisterSuccess(registerResponse: RegisterResponse): ResultAsyncState<Unit> {
        return try {
            val authToken = AuthToken(
                accessToken = registerResponse.accessToken,
                refreshToken = registerResponse.refreshToken,
                tokenType = registerResponse.tokenType,
                expiresIn = registerResponse.expiresIn,
            )
            tokenManager.saveTokens(authToken, registerResponse.user)
            ResultAsyncState.Success(Unit)
        } catch (e: Exception) {
            ResultAsyncState.Error("Ocorreu um erro ao processar os dados de registro: ${e.message}")
        }
    }

    override suspend fun checkAuthenticationStatus(): Boolean {
        return try {

            if (tokenManager.isAuthenticated.value) {
                if (tokenManager.isTokenValid()) {
                    true
                } else {
                   // implementar lógica de refresh token
                    return  false
                }
            } else {
                println("Usuário não autenticado")
                false
            }
        } catch (e: Exception) {
            println("Erro ao verificar status de autenticação: ${e.message}")
            false
        }
    }
}

object AuthenticationManagerFactory {
    fun create(
        tokenManager: TokenManager,
    ): IAuthenticationManagerService {
        return AuthenticationManager(
            tokenManager = tokenManager,
        )
    }
}

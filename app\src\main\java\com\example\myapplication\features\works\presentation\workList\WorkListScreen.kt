package com.example.myapplication.features.works.presentation.workList

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.example.myapplication.core.common.ResultAsyncState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Info
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.size
import coil.compose.AsyncImage
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.foundation.clickable
import androidx.compose.runtime.remember
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel

@Composable
fun WorkListScreen(
    viewModel: WorkListViewModel
) {
    val uiState by viewModel.uiState.collectAsState()
    val addWorkViewModel: AddWorkViewModel = viewModel()


    LaunchedEffect(Unit) {
        viewModel.onEvent(WorkListUiEvent.LoadWorks)
    }



    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when {
            uiState.isLoading -> {
                CircularProgressIndicator()
            }
            uiState.error != null -> {
                Column(horizontalAlignment =  Alignment.CenterHorizontally) {
                    Text(text = "Erro: ${uiState.error}")
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(onClick =  { viewModel.onEvent(WorkListUiEvent.LoadWorks)}) {
                        Text("Tentar novamente")
                    }
                }
            }
            true -> {
                val works = uiState.works.data?.works
                if (works?.isNotEmpty() == true) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        works.forEach { work ->
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(8.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = Color(0xFFF5F5F5)
                                ),
                                elevation = CardDefaults.elevatedCardElevation(8.dp),
                                shape = RoundedCornerShape(16.dp)
                            ) {
                                Row(modifier = Modifier.padding(16.dp)) {
                                    Spacer(modifier = Modifier.width(16.dp))
                                    Column(modifier = Modifier.weight(1f)) {
                                        Text(
                                            text = work.title,
                                            style = MaterialTheme.typography.titleLarge,
                                            color = MaterialTheme.colorScheme.primary
                                        )
                                        if (work.originalTitle.isNotBlank() && work.originalTitle != work.title) {
                                            Text(
                                                text = work.originalTitle,
                                                style = MaterialTheme.typography.labelMedium,
                                                color = MaterialTheme.colorScheme.secondary
                                            )
                                        }
                                        Spacer(modifier = Modifier.height(4.dp))
                                        Text(
                                            text = "Autor: ${work.author} | Artista: ${work.artist}",
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                        Text(
                                            text = "Tipo: ${work.type} | Status: ${work.status}",
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                        Text(
                                            text = "Capítulos: ${work.totalChapters} | Lançamento: ${work.releaseDate}",
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                        Spacer(modifier = Modifier.height(6.dp))
                                        Text(
                                            text = work.description,
                                            style = MaterialTheme.typography.bodySmall,
                                            color = Color.DarkGray,
                                            maxLines = 3
                                        )
                                        Spacer(modifier = Modifier.height(8.dp))
                                        Row(verticalAlignment = Alignment.CenterVertically) {
                                            Icon(
                                                imageVector = Icons.Default.Info,
                                                contentDescription = "Info",
                                                tint = MaterialTheme.colorScheme.primary,
                                                modifier = Modifier.size(18.dp)
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text(
                                                text = "ID: ${work.id}",
                                                style = MaterialTheme.typography.labelSmall
                                            )
                                            Spacer(modifier = Modifier.width(12.dp))
                                            Text(
                                                text = "Nota: ${"%.1f".format(work.averageRating)} (${work.totalReviews} avaliações)",
                                                style = MaterialTheme.typography.labelSmall
                                            )
                                        }
                                        Spacer(modifier = Modifier.height(4.dp))
                                        Text(
                                            text = "Criado em: ${work.createdAt} | Atualizado em: ${work.updatedAt}",
                                            style = MaterialTheme.typography.labelSmall,
                                            color = Color.Gray
                                        )
                                    }
                                }
                            }
                        }
                    }
                } else {
                    Text("Nenhuma obra disponível.")
                }
            }
            else -> {
                Text("Nenhum dado de obras disponível.")
            }
        }
    }
    // Botão flutuante para adicionar obra
    FloatingActionButton(
        onClick = { addWorkViewModel.openDialog() },
        modifier = Modifier

            .padding(16.dp)


//            .align(Alignment.BottomEnd)
//            .padding(24.dp)
    ) {
        Icon(imageVector = Icons.Default.Add, contentDescription = "Adicionar Obra")
    }
    // Modal para adicionar obra
    AddWorkDialog(
        show = addWorkViewModel.showDialog,
        onDismiss = { addWorkViewModel.closeDialog() },
        onAdd = { addWorkViewModel.addWork() }
    )
}



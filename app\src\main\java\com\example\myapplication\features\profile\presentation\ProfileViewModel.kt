package com.example.myapplication.features.profile.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.auth.data.remote.dto.profile.ProfileResponseDto
import com.example.myapplication.features.auth.domain.useCase.GetProfileUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch


data class ProfileUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val userProfile: ProfileResponseDto? = null
)

sealed class ProfileUiEvent {
    data object LoadProfile : ProfileUiEvent()
    data object ClearError : ProfileUiEvent()
}

class ProfileViewModel(
    private val getUserProfileUseCase: GetProfileUseCase
) : ViewModel() {
    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState = _uiState

    fun onEvent(event: ProfileUiEvent) {
        when (event) {
            is ProfileUiEvent.LoadProfile -> {
                loadProfile()
            }
            is ProfileUiEvent.ClearError -> {
                _uiState.value = _uiState.value.copy(error = null)
            }
        }
    }


    private fun loadProfile() {
        _uiState.value = _uiState.value.copy(isLoading = true, error = null)
        viewModelScope.launch {
            getUserProfileUseCase().collect { result ->
                when (result) {
                    is ResultAsyncState.Loading -> {
                        _uiState.value = _uiState.value.copy(isLoading = true)
                    }
                    is ResultAsyncState.Success -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            userProfile = result.data,
                            error = null
                        )
                    }
                    is ResultAsyncState.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = result.message
                        )
                    }
                }
            }
        }
    }

}


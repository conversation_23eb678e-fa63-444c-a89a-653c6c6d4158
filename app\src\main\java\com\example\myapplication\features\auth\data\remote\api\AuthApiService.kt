package com.example.myapplication.features.auth.data.remote.api

import com.example.myapplication.data.remote.dto.BaseResponseDto

import com.example.myapplication.features.auth.data.remote.dto.register.RegisterRequestDto
import com.example.myapplication.features.auth.data.remote.dto.login.LoginRequestDto
import com.example.myapplication.features.auth.data.remote.dto.login.LoginResponseDto
import com.example.myapplication.features.auth.data.remote.dto.profile.ProfileResponseDto
import com.example.myapplication.features.auth.data.remote.dto.register.RegisterResponseDto

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

interface AuthApiService {
    @POST("/auth/login")
    suspend fun login(@Body request: LoginRequestDto): Response<BaseResponseDto<LoginResponseDto>>

    @POST("/auth/register")
    suspend fun register(@Body request: RegisterRequestDto): Response<BaseResponseDto<RegisterResponseDto>>

    @GET("/auth/profile")
    suspend fun getUserProfile(): Response<BaseResponseDto<ProfileResponseDto>>
}
package com.example.myapplication.features.works.domain.useCase

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.dto.GetWorksDto
import com.example.myapplication.features.works.data.remote.dto.GetWorksParametersDto
import com.example.myapplication.features.works.domain.repository.WorksRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class GetWorksUseCase(
    private val worksRepository: WorksRepository
) {
    operator fun invoke(params: GetWorksParametersDto? = null): Flow<ResultAsyncState<GetWorksDto>> = flow {
        try {
            val response = worksRepository.getWorks(params)
            emit(response)
        } catch (e: Exception) {
            emit(ResultAsyncState.Error("Erro ao obter as obras: ${e.message}"))
        }
    }
}
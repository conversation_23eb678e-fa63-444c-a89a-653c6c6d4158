package com.example.myapplication.ui.theme

import androidx.compose.ui.graphics.Color

// Cores Principais
val PrimaryDark = Color(0xFF4B0082) // Indigo Dye
val Secondary = Color(0xFF1E90FF) // Dodger Blue
val NeutralDark = Color(0xFF2D2D2D) // Charcoal
val NeutralLight = Color(0xFFE6E6FA) // Lavender Mist
val AccentRed = Color(0xFFFF4040) // Coral Red
val AccentYellow = Color(0xFFFFD700) // Gold

// Variações para o tema escuro
val PrimaryLight = Color(0xFF6B2BA2) // Versão mais clara do Indigo
val SecondaryDark = Color(0xFF0066CC) // Versão mais escura do Dodger Blue
val NeutralDarkVariant = Color(0xFF1A1A1A) // Versão mais escura do Charcoal
val NeutralLightVariant = Color(0xFFF8F8FF) // Versão mais clara do Lavender Mist

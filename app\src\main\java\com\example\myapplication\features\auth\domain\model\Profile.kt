package com.example.myapplication.features.auth.domain.model

data class ProfileResponse(
    val id: Int,
    val username: String,
    val email: String,
    val fullName: String,
    val avatar: String,
    val isActive: Boolean,
    val role: String,
    val createdAt: String,
    val updatedAt: String,
    val stats: Stats
)

data class Stats(
    val readingStats: ReadingStats,
    val totalChaptersRead: Int,
    val totalLists: Int,
    val totalPublicLists: Int,
    val totalRankings: Int,
    val totalPublicRankings: Int,
    val totalReviews: Int,
    val totalPublicReviews: Int,
    val averageRating: Double,
    val totalReviewLikes: Int,
    val averageReadingTime: Double,
    val lastReadingActivity: String,
    val favoriteGenre: String
)

data class ReadingStats(
    val reading: Int,
    val completed: Int,
    val dropped: Int,
    val planToRead: Int,
    val onHold: Int,
    val totalWorks: Int
)
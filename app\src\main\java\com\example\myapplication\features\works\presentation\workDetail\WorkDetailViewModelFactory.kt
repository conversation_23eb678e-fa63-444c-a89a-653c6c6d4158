package com.example.myapplication.features.works.presentation.workDetail

import App<PERSON><PERSON>rDI
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.example.myapplication.features.works.domain.useCase.*

class WorkDetailViewModelFactory(
    private val appContainer: AppContainerDI
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(WorkDetailViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return WorkDetailViewModel(
                getWorkByIdUseCase = GetWorkByIdUseCase(
                    worksRepository = appContainer.worksRepository
                ),
                updateWorkUseCase = UpdateWorkUseCase(
                    worksRepository = appContainer.worksRepository
                ),
                deleteWorkUseCase = DeleteWorkUseCase(
                    worksRepository = appContainer.worksRepository
                ),
                createChapterUseCase = CreateChapterUseCase(
                    worksRepository = appContainer.worksRepository
                ),
                setCoverImageUseCase = SetCoverImageUseCase(
                    worksRepository = appContainer.worksRepository
                ),
                removeCoverImageUseCase = RemoveCoverImageUseCase(
                    worksRepository = appContainer.worksRepository
                ),
                addGalleryImagesUseCase = AddGalleryImagesUseCase(
                    worksRepository = appContainer.worksRepository
                ),
                removeGalleryImageUseCase = RemoveGalleryImageUseCase(
                    worksRepository = appContainer.worksRepository
                )
            ) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
    }
}

//package com.example.myapplication.features.profile.data.remote
//
//import com.example.myapplication.data.remote.dto.BaseResponseDto
//import com.example.myapplication.data.remote.dto.ProfileResponseDto
//import retrofit2.Response
//import retrofit2.http.GET
//
//interface ProfileApiService {
//    @GET("/auth/profile")
//    suspend fun getUserProfile(): Response<BaseResponseDto<ProfileResponseDto>>
//}
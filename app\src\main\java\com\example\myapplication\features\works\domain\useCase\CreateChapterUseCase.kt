package com.example.myapplication.features.works.domain.useCase

import com.example.myapplication.core.common.ResultAsyncState
import com.example.myapplication.features.works.data.remote.dto.ChapterDto
import com.example.myapplication.features.works.data.remote.dto.CreateChapterDto
import com.example.myapplication.features.works.domain.repository.WorksRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class CreateChapterUseCase(
    private val worksRepository: WorksRepository
) {
    operator fun invoke(createChapterRequest: CreateChapterDto): Flow<ResultAsyncState<ChapterDto>> = flow {
        try {
            emit(ResultAsyncState.Loading())
            val response = worksRepository.createChapter(createChapterRequest)
            emit(response)
        } catch (e: Exception) {
            emit(ResultAsyncState.Error("Erro ao criar capítulo: ${e.message}"))
        }
    }
}

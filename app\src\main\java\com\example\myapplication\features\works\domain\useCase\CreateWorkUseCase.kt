package com.example.myapplication.features.works.domain.useCase

import com.example.myapplication.features.works.data.remote.dto.CreateWorkDto
import com.example.myapplication.features.works.domain.repository.WorksRepository


class CreateWorkUseCase(
    private val worksRepository: WorksRepository
) {
    suspend operator fun invoke(createWork: CreateWorkDto) {
        when {
            createWork.title.isBlank() -> throw IllegalArgumentException("O título não pode estar vazio.")
            createWork.originalTitle.isBlank() -> throw IllegalArgumentException("O título original não pode estar vazio.")
            createWork.description.isBlank() -> throw IllegalArgumentException("A descrição não pode estar vazia.")
            createWork.coverImage.isBlank() -> throw IllegalArgumentException("A imagem de capa não pode estar vazia.")
            createWork.type.isBlank() -> throw IllegalArgumentException("O tipo não pode estar vazio.")
            createWork.status.isBlank() -> throw IllegalArgumentException("O status não pode estar vazio.")
            createWork.author.isBlank() -> throw IllegalArgumentException("O autor não pode estar vazio.")
            createWork.artist.isBlank() -> throw IllegalArgumentException("O artista não pode estar vazio.")
            createWork.totalChapters <= 0 -> throw IllegalArgumentException("O número total de capítulos deve ser maior que zero.")
            createWork.releaseDate.isBlank() -> throw IllegalArgumentException("A data de lançamento não pode estar vazia.")
        }

        worksRepository.createWork(createWorkRequest =  createWork)
    }
}

package com.example.myapplication.features.works.presentation.workList

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun AddWorkDialog(
    show: <PERSON><PERSON><PERSON>,
    onDismiss: () -> Unit,
    onAdd: () -> Unit
) {
    if (show) {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text("Adicionar Obra") },
            text = {
                Column {
                    OutlinedTextField(value = "Título Mock", onValueChange = {}, label = { Text("Título") })
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(value = "Autor Mock", onValueChange = {}, label = { Text("Autor") })
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(value = "Descrição Mock", onValueChange = {}, label = { Text("Descrição") })
                }
            },
            confirmButton = {
                Button(onClick = onAdd) {
                    Text("Adicionar")
                }
            },
            dismissButton = {
                Button(onClick = onDismiss) {
                    Text("Cancelar")
                }
            }
        )
    }
}


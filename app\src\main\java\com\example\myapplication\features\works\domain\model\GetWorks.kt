package com.example.myapplication.features.works.domain.model

import com.example.myapplication.core.common.PaginatedData

data class Work(
    val id: String,
    val title: String,
    val originalTitle: String,
    val description: String,
    val coverImage: String,
    val type: String,
    val status: String,
    val author: String,
    val artist: String,
    val totalChapters: Int,
    val releaseDate: String,
    val averageRating: Double,
    val totalReviews: Int,
    val createdAt: String,
    val updatedAt: String
)

interface WorkPaginatedData: PaginatedData {
    val works: List<Work>
}

data class GetWorks(
    override val total: Int,
    override val page: Int,
    override val limit: Int,
    override val totalPages: Int,
    override val works: List<Work>
): WorkPaginatedData
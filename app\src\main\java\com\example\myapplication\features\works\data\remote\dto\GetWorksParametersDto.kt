package com.example.myapplication.features.works.data.remote.dto
import com.example.myapplication.features.works.domain.enums.*
import com.google.gson.annotations.SerializedName


data class GetWorksParametersDto(
    @SerializedName("type")
    val type: WorkType? = null,

    @SerializedName("status")
    val status: WorkStatus? = null,

    @SerializedName("author")
    val author: String? = null,

    @SerializedName("artist")
    val artist: String? = null,

    @SerializedName("search")
    val search: String? = null,

    @SerializedName("tags")
    val tags: List<String>? = null,

    @SerializedName("minRating")
    val minRating: Float? = null,

    @SerializedName("maxRating")
    val maxRating: Float? = null,

    @SerializedName("page")
    val page: Int? = 1,

    @SerializedName("limit")
    val limit: Int? = 20,

    @SerializedName("sortBy")
    val sortBy: WorkSortBy? = null,

    @SerializedName("sortOrder")
    val sortOrder: WorkSortOrder? = null
)
package com.example.myapplication.features.works.data.remote.dto

import com.google.gson.annotations.SerializedName

data class CreateWorkDto(
    @SerializedName("title")
    val title: String,
    @SerializedName("originalTitle")
    val originalTitle: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("coverImage")
    val coverImage: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("status")
    val status: String,
    @SerializedName("author")
    val author: String,
    @SerializedName("artist")
    val artist: String,
    @SerializedName("totalChapters")
    val totalChapters: Int,
    @SerializedName("releaseDate")
    val releaseDate: String
)